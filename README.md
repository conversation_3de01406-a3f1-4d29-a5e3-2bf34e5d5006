# Guess My Age - AI Age Estimation App

A modern web application that uses AI to estimate age from photos. Built with Next.js, Supabase, and OpenAI.

## Features

- 🤖 **AI-Powered Age Estimation** - Uses OpenAI GPT-4o-mini for accurate age analysis
- 👤 **User Authentication** - Secure signup/login with Supabase Auth
- 💳 **Credit System** - Purchase credits for additional analyses
- 📊 **Usage Limits** - 1 free analysis per day for anonymous users, 3 for registered users
- 📱 **Responsive Design** - Modern, clean UI that works on all devices
- 📈 **Analysis History** - Track your previous age estimations
- 🔒 **Privacy First** - Photos are analyzed but not stored on servers

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: OpenAI GPT-4o-mini Vision API
- **Payments**: Stripe
- **Deployment**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account
- OpenAI API key
- Stripe account

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd guess-my-age
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your environment variables:
   ```env
   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # OpenAI
   OPENAI_API_KEY=your_openai_api_key

   # Stripe
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_webhook_secret

   # App
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

3. **Set up Supabase Database**

   The database tables and functions are automatically created when you first run the app. The schema includes:
   - `user_profiles` - User information and credits
   - `photo_analyses` - Analysis history
   - `anonymous_uses` - Anonymous usage tracking
   - `credit_transactions` - Payment history

4. **Configure Stripe Webhooks**

   Set up a webhook endpoint in your Stripe dashboard:
   - URL: `https://your-domain.com/api/webhooks/stripe`
   - Events: `payment_intent.succeeded`, `payment_intent.payment_failed`

5. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

### For Anonymous Users
- Upload 1 photo per day for free age estimation
- No account required

### For Registered Users
- Upload 3 photos per day for free
- Purchase additional credits (5 credits for $5)
- View analysis history
- Account dashboard with usage tracking

### Credit System
- Each analysis consumes 1 credit or daily use
- Credits are purchased in packages of 5 for $5.00
- Credits never expire

## API Endpoints

- `POST /api/analyze` - Analyze photo for age estimation
- `GET /api/history` - Get user's analysis history
- `GET /api/usage` - Check current usage limits
- `POST /api/create-payment-intent` - Create Stripe payment
- `POST /api/webhooks/stripe` - Handle Stripe webhooks

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support, email [your-email] or create an issue on GitHub.

## Privacy & Security

- Photos are analyzed in real-time and not stored
- User data is encrypted and secured with Supabase
- Payments are processed securely through Stripe
- GDPR compliant data handling
