'use client'

import { useState } from 'react'
import { User, CreditCard, LogOut, Calendar } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { formatCredits } from '@/lib/utils'

interface UserProfileProps {
  onBuyCredits: () => void
}

export default function UserProfile({ onBuyCredits }: UserProfileProps) {
  const { user, profile, signOut } = useAuth()
  const [showDropdown, setShowDropdown] = useState(false)

  if (!user || !profile) return null

  const remainingDailyUses = Math.max(0, 3 - profile.daily_uses)
  const isNewDay = profile.last_use_date !== new Date().toISOString().split('T')[0]
  const actualRemainingUses = isNewDay ? 3 : remainingDailyUses

  return (
    <div className="relative">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 hover:bg-gray-50 transition-colors"
      >
        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-blue-600" />
        </div>
        <div className="text-left">
          <div className="text-sm font-medium text-gray-900">
            {user.email?.split('@')[0]}
          </div>
          <div className="text-xs text-gray-500">
            {formatCredits(profile.credits)}
          </div>
        </div>
      </button>

      {showDropdown && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowDropdown(false)}
          />
          <div className="absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">{user.email}</div>
                  <div className="text-sm text-gray-500">
                    Member since {new Date(profile.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Daily Uses</span>
                  </div>
                  <div className="text-lg font-bold text-blue-900 mt-1">
                    {actualRemainingUses}/3
                  </div>
                  <div className="text-xs text-blue-700">
                    {isNewDay ? 'Refreshed today' : 'Remaining today'}
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Credits</span>
                  </div>
                  <div className="text-lg font-bold text-green-900 mt-1">
                    {profile.credits}
                  </div>
                  <div className="text-xs text-green-700">
                    Extra analyses
                  </div>
                </div>
              </div>

              <button
                onClick={() => {
                  onBuyCredits()
                  setShowDropdown(false)
                }}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
              >
                <CreditCard className="w-4 h-4" />
                <span>Buy More Credits</span>
              </button>

              <button
                onClick={() => {
                  signOut()
                  setShowDropdown(false)
                }}
                className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
