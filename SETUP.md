# Setup Guide - Guess My Age

## Quick Start Checklist

### 1. Prerequisites
- [ ] Node.js 18+ installed
- [ ] Git installed
- [ ] Supabase account created
- [ ] OpenAI API key obtained
- [ ] Stripe account set up

### 2. Project Setup
- [ ] Clone repository
- [ ] Install dependencies: `npm install`
- [ ] Copy environment variables: `cp .env.example .env.local`

### 3. Supabase Configuration
- [ ] Create new Supabase project
- [ ] Copy project URL and API keys to `.env.local`
- [ ] Run database setup (tables will be created automatically)

### 4. OpenAI Setup
- [ ] Get OpenAI API key from https://platform.openai.com/api-keys
- [ ] Add key to `.env.local` as `OPENAI_API_KEY`

### 5. Stripe Setup
- [ ] Create Stripe account
- [ ] Get publishable and secret keys
- [ ] Set up webhook endpoint: `https://your-domain.com/api/webhooks/stripe`
- [ ] Add webhook events: `payment_intent.succeeded`, `payment_intent.payment_failed`
- [ ] Add all keys to `.env.local`

### 6. Development
- [ ] Run `npm run dev`
- [ ] Test application at http://localhost:3000

### 7. Deployment
- [ ] Push code to GitHub
- [ ] Connect repository to Vercel
- [ ] Add environment variables in Vercel dashboard
- [ ] Deploy

## Detailed Instructions

### Environment Variables Setup

Create `.env.local` with these values:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# OpenAI
OPENAI_API_KEY=sk-your-openai-key

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Database Schema

The application automatically creates these tables:

1. **user_profiles** - User information and credits
2. **photo_analyses** - Analysis history
3. **anonymous_uses** - Anonymous usage tracking
4. **credit_transactions** - Payment history

### Stripe Webhook Configuration

1. Go to Stripe Dashboard > Webhooks
2. Add endpoint: `https://your-domain.com/api/webhooks/stripe`
3. Select events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
4. Copy webhook secret to environment variables

### Testing

#### Test Anonymous Usage
1. Visit application without signing in
2. Upload a photo
3. Verify 1 analysis per day limit

#### Test User Registration
1. Click "Sign In" and create account
2. Verify email confirmation
3. Test 3 analyses per day limit

#### Test Credit Purchase
1. Sign in to account
2. Click "Buy Credits"
3. Use Stripe test card: 4242 4242 4242 4242
4. Verify credits are added to account

#### Test Photo Analysis
1. Upload clear photo with visible face
2. Verify age estimation is reasonable
3. Check analysis appears in history

### Troubleshooting

#### Common Issues

**"Invalid URL" Error**
- Check Supabase URL format: `https://project.supabase.co`
- Ensure no trailing slashes

**OpenAI API Errors**
- Verify API key is correct
- Check OpenAI account has credits
- Ensure image is under 10MB

**Stripe Webhook Failures**
- Verify webhook URL is accessible
- Check webhook secret matches
- Ensure HTTPS in production

**Database Connection Issues**
- Verify Supabase project is active
- Check service role key permissions
- Ensure RLS policies are configured

#### Development Tips

1. **Hot Reload**: Changes to components update automatically
2. **API Testing**: Use tools like Postman or curl
3. **Database Inspection**: Use Supabase dashboard
4. **Logs**: Check browser console and terminal output

### Production Deployment

#### Vercel (Recommended)

1. Push code to GitHub
2. Import project in Vercel
3. Add environment variables
4. Deploy

#### Environment Variables for Production

Update these for production:
- `NEXT_PUBLIC_APP_URL`: Your production domain
- Use production Stripe keys
- Use production OpenAI key

#### Security Checklist

- [ ] Environment variables are secure
- [ ] Stripe webhook endpoint uses HTTPS
- [ ] Supabase RLS policies are enabled
- [ ] API keys are not exposed in client code
- [ ] CORS is properly configured

### Monitoring

#### Key Metrics to Track

1. **Usage**: Daily/monthly analyses
2. **Revenue**: Credit purchases
3. **Performance**: API response times
4. **Errors**: Failed analyses or payments

#### Recommended Tools

- Vercel Analytics for performance
- Stripe Dashboard for payments
- Supabase Dashboard for database
- OpenAI Usage Dashboard for API costs

### Support

For issues:
1. Check this setup guide
2. Review error logs
3. Test with minimal example
4. Create GitHub issue with details
