import { NextRequest, NextResponse } from 'next/server'
import { verifyWebhookSignature } from '@/lib/stripe'
import { getSupabaseAdminClient } from '@/lib/supabase'
import { addCredits } from '@/lib/credits'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')
    
    if (!signature) {
      return NextResponse.json({ error: 'No signature' }, { status: 400 })
    }

    // Verify webhook signature
    const event = verifyWebhookSignature(body, signature)
    if (!event) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    // Handle payment intent succeeded
    if (event.type === 'payment_intent.succeeded') {
      const paymentIntent = event.data.object
      const userId = paymentIntent.metadata.userId
      const credits = parseInt(paymentIntent.metadata.credits || '0')
      
      if (!userId || !credits) {
        console.error('Missing metadata in payment intent:', paymentIntent.id)
        return NextResponse.json({ error: 'Invalid metadata' }, { status: 400 })
      }

      // Update transaction status
      const supabaseAdmin = getSupabaseAdminClient()
      await supabaseAdmin
        .from('credit_transactions')
        .update({ status: 'completed' })
        .eq('stripe_payment_intent_id', paymentIntent.id)

      // Add credits to user account
      const success = await addCredits(userId, credits)
      
      if (!success) {
        console.error('Failed to add credits for user:', userId)
        return NextResponse.json({ error: 'Failed to add credits' }, { status: 500 })
      }

      console.log(`Added ${credits} credits to user ${userId}`)
    }

    // Handle payment intent failed
    if (event.type === 'payment_intent.payment_failed') {
      const paymentIntent = event.data.object

      // Update transaction status
      const supabaseAdmin = getSupabaseAdminClient()
      await supabaseAdmin
        .from('credit_transactions')
        .update({ status: 'failed' })
        .eq('stripe_payment_intent_id', paymentIntent.id)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Stripe webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}
