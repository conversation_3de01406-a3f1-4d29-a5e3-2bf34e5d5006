# API Documentation

## Overview

The Guess My Age application provides several API endpoints for photo analysis, user management, and payment processing.

## Authentication

Most endpoints require authentication via Supabase Auth. Include the user's JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Endpoints

### POST /api/analyze

Analyze a photo to estimate age.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: FormData with 'image' field containing the photo file

**Response:**
```json
{
  "success": true,
  "analysis": {
    "estimatedAge": 25,
    "confidence": 0.85,
    "hasPersonDetected": true,
    "explanation": "Clear facial features visible, appears to be a young adult..."
  },
  "usage": {
    "remainingUses": 2,
    "isAnonymous": false,
    "needsCredits": false
  },
  "analysisId": "uuid"
}
```

**Error Response:**
```json
{
  "error": "Daily limit reached. Please try again tomorrow or create an account for more uses.",
  "needsCredits": true,
  "isAnonymous": true
}
```

### GET /api/history

Get user's analysis history (requires authentication).

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Response:**
```json
{
  "analyses": [
    {
      "id": "uuid",
      "estimated_age": 25,
      "confidence_score": 0.85,
      "analysis_result": {
        "hasPersonDetected": true,
        "explanation": "..."
      },
      "created_at": "2025-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "totalPages": 5
  }
}
```

### GET /api/usage

Check current usage limits and user status.

**Response:**
```json
{
  "canUse": true,
  "remainingUses": 3,
  "isAnonymous": false,
  "needsCredits": false,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "profile": {
      "credits": 10,
      "daily_uses": 0,
      "last_use_date": "2025-01-01"
    }
  }
}
```

### POST /api/create-payment-intent

Create a Stripe payment intent for purchasing credits (requires authentication).

**Request:**
```json
{
  "packageType": "basic"
}
```

**Response:**
```json
{
  "clientSecret": "pi_xxx_secret_xxx",
  "paymentIntentId": "pi_xxx",
  "package": {
    "name": "5 Credits",
    "credits": 5,
    "price": 500,
    "description": "Perfect for occasional use"
  }
}
```

### POST /api/webhooks/stripe

Stripe webhook endpoint for handling payment events.

**Headers:**
- `stripe-signature`: Stripe webhook signature

**Events Handled:**
- `payment_intent.succeeded`: Credits are added to user account
- `payment_intent.payment_failed`: Transaction is marked as failed

## Rate Limits

### Anonymous Users
- 1 photo analysis per day per IP address

### Registered Users
- 3 photo analyses per day (free)
- Additional analyses consume credits (1 credit = 1 analysis)

## Error Codes

- `400`: Bad Request - Invalid input or missing parameters
- `401`: Unauthorized - Authentication required
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server-side error

## Credit System

### Packages Available
- **Basic Package**: 5 credits for $5.00 USD

### Credit Usage
- Each photo analysis consumes 1 credit or 1 daily use
- Daily uses are reset at midnight UTC
- Credits never expire
- Credits are consumed only after daily uses are exhausted

## Image Requirements

### Supported Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)

### Size Limits
- Maximum file size: 10MB
- Recommended: Under 5MB for faster processing

### Quality Guidelines
- Clear, well-lit photos work best
- Face should be clearly visible
- Avoid heavily filtered or edited images
- Single person per photo recommended

## Privacy & Security

- Photos are analyzed in real-time and not stored on servers
- Analysis results are stored in encrypted database
- User data is protected with Row Level Security (RLS)
- All API communications use HTTPS
- Stripe handles payment processing securely
