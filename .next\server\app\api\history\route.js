(()=>{var e={};e.id=670,e.ids=[670],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1010:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(6559),o=t(8088),a=t(7719),n=t(2190),u=t(3769);async function p(e){try{let r=await (0,u.d)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:i}=new URL(e.url),o=parseInt(i.get("page")||"1"),a=parseInt(i.get("limit")||"10"),p=(o-1)*a,{data:c,error:d}=await r.from("photo_analyses").select("*").eq("user_id",t.id).order("created_at",{ascending:!1}).range(p,p+a-1);if(d)return console.error("Error fetching analyses:",d),n.NextResponse.json({error:"Failed to fetch history"},{status:500});let{count:l,error:x}=await r.from("photo_analyses").select("*",{count:"exact",head:!0}).eq("user_id",t.id);return x&&console.error("Error counting analyses:",x),n.NextResponse.json({analyses:c||[],pagination:{page:o,limit:a,total:l||0,totalPages:Math.ceil((l||0)/a)}})}catch(e){return console.error("History API error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/history/route",pathname:"/api/history",filename:"route",bundlePath:"app/api/history/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\history\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function g(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,r,t)=>{"use strict";t.d(r,{d:()=>o});var s=t(4386),i=t(4999);let o=async()=>{let e=await (0,i.UL)();return(0,s.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){e.set({name:r,value:t,...s})},remove(r,t){e.set({name:r,value:"",...t})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,410],()=>t(1010));module.exports=s})();