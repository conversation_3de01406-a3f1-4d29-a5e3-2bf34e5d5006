import { createSupabaseServerClient } from './supabase-server'
import { getSupabaseAdminClient } from './supabase'

export interface UsageCheck {
  canUse: boolean
  remainingUses: number
  isAnonymous: boolean
  needsCredits: boolean
  canUseLevel?: {
    low: boolean
    medium: boolean
    high: boolean
  }
}

// Check if user can make an analysis
export async function checkUsageLimit(userId?: string, ipAddress?: string): Promise<UsageCheck> {
  const supabase = await createSupabaseServerClient()
  
  if (userId) {
    // Registered user - check daily limit and credits
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (!profile) {
      return { canUse: false, remainingUses: 0, isAnonymous: false, needsCredits: false }
    }
    
    // Reset daily uses if it's a new day
    const today = new Date().toISOString().split('T')[0]
    const lastUseDate = profile.last_use_date
    
    let dailyUses = profile.daily_uses
    if (lastUseDate !== today) {
      dailyUses = 0
      await supabase
        .from('user_profiles')
        .update({ daily_uses: 0, last_use_date: today })
        .eq('id', userId)
    }
    
    const remainingDaily = Math.max(0, 3 - dailyUses)
    const totalAvailable = remainingDaily + profile.credits

    // Calculate which detail levels are available
    const canUseLevel = {
      low: totalAvailable >= 1,
      medium: totalAvailable >= 2,
      high: totalAvailable >= 3
    }

    const canUse = totalAvailable > 0

    return {
      canUse,
      remainingUses: totalAvailable,
      isAnonymous: false,
      needsCredits: !canUse,
      canUseLevel
    }
  } else {
    // Anonymous user - check IP-based daily limit (1 per day)
    if (!ipAddress) {
      return { canUse: false, remainingUses: 0, isAnonymous: true, needsCredits: false }
    }
    
    const today = new Date().toISOString().split('T')[0]
    const supabaseAdmin = getSupabaseAdminClient()
    const { data: anonymousUses } = await supabaseAdmin
      .from('anonymous_uses')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('use_date', today)
    
    const usesToday = anonymousUses?.length || 0
    const canUse = usesToday < 1

    // Anonymous users can only use low level (1 credit)
    const canUseLevel = {
      low: canUse,
      medium: false,
      high: false
    }

    return {
      canUse,
      remainingUses: canUse ? 1 : 0,
      isAnonymous: true,
      needsCredits: false,
      canUseLevel
    }
  }
}

// Consume a use (either daily use or credit)
export async function consumeUse(userId?: string, ipAddress?: string, creditsToConsume: number = 1): Promise<boolean> {
  const supabase = await createSupabaseServerClient()
  
  if (userId) {
    // Registered user
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (!profile) return false
    
    // Check if it's a new day and reset daily uses
    const today = new Date().toISOString().split('T')[0]
    let dailyUses = profile.daily_uses
    
    if (profile.last_use_date !== today) {
      dailyUses = 0
    }
    
    // Calculate available resources
    const remainingDaily = Math.max(0, 3 - dailyUses)
    const totalAvailable = remainingDaily + profile.credits

    // Check if we have enough resources
    if (totalAvailable < creditsToConsume) {
      return false
    }

    // Use daily uses first, then credits
    let creditsNeeded = creditsToConsume
    let newDailyUses = dailyUses
    let newCredits = profile.credits

    // Use daily uses first
    if (remainingDaily > 0) {
      const dailyToUse = Math.min(remainingDaily, creditsNeeded)
      newDailyUses += dailyToUse
      creditsNeeded -= dailyToUse
    }

    // Use credits for remaining
    if (creditsNeeded > 0) {
      newCredits -= creditsNeeded
    }

    await supabase
      .from('user_profiles')
      .update({
        daily_uses: newDailyUses,
        credits: newCredits,
        last_use_date: today,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)

    return true
  } else {
    // Anonymous user
    if (!ipAddress) return false
    
    const today = new Date().toISOString().split('T')[0]
    const supabaseAdmin = getSupabaseAdminClient()

    // Check if already used today
    const { data: existingUse } = await supabaseAdmin
      .from('anonymous_uses')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('use_date', today)
      .single()
    
    if (existingUse) return false
    
    // Record the use
    await supabaseAdmin
      .from('anonymous_uses')
      .insert({ ip_address: ipAddress, use_date: today })
    
    return true
  }
}

// Add credits to user account
export async function addCredits(userId: string, credits: number): Promise<boolean> {
  const supabase = await createSupabaseServerClient()
  
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('credits')
    .eq('id', userId)
    .single()
  
  if (!profile) return false
  
  await supabase
    .from('user_profiles')
    .update({ 
      credits: profile.credits + credits,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
  
  return true
}
