(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[81],{306:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},313:(e,t,n)=>{"use strict";n.d(t,{lG:()=>tS});var r,s,i,o,a,l=n(2115),u=n.t(l,2),c=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.<PERSON>="ArrowLeft",e.ArrowUp="ArrowUp",e.<PERSON>ight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(c||{}),d=Object.defineProperty,h=(e,t,n)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,f=(e,t,n)=>(h(e,"symbol"!=typeof t?t+"":t,n),n);class p{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){f(this,"current",this.detect()),f(this,"handoffState","pending"),f(this,"currentId",0)}}let m=new p,g=(e,t)=>{m.isServer?(0,l.useEffect)(e,t):(0,l.useLayoutEffect)(e,t)};function y(e){let t=(0,l.useRef)(e);return g(()=>{t.current=e},[e]),t}function v(e,t,n,r){let s=y(n);(0,l.useEffect)(()=>{function n(e){s.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class w extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}constructor(e){super(),this.factory=e}}function b(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function _(){let e=[],t={addEventListener:(e,n,r,s)=>(e.addEventListener(n,r,s),t.add(()=>e.removeEventListener(n,r,s))),requestAnimationFrame(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let s=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(s))},nextFrame(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let s=setTimeout(...n);return t.add(()=>clearTimeout(s))},microTask(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let s={current:!0};return b(()=>{s.current&&n[0]()}),t.add(()=>{s.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(e){let t=_();return e(t),this.add(()=>t.dispose())},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}var E=Object.defineProperty,S=(e,t,n)=>t in e?E(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,k=(e,t,n)=>(S(e,"symbol"!=typeof t?t+"":t,n),n),x=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},A=(e,t,n)=>(x(e,t,"read from private field"),n?n.call(e):t.get(e)),C=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},O=(e,t,n,r)=>(x(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class P{dispose(){this.disposables.dispose()}get state(){return A(this,r)}subscribe(e,t){let n={selector:e,callback:t,current:e(A(this,r))};return A(this,i).add(n),this.disposables.add(()=>{A(this,i).delete(n)})}on(e,t){return A(this,s).get(e).add(t),this.disposables.add(()=>{A(this,s).get(e).delete(t)})}send(e){let t=this.reduce(A(this,r),e);if(t!==A(this,r)){for(let e of(O(this,r,t),A(this,i))){let t=e.selector(A(this,r));R(e.current,t)||(e.current=t,e.callback(t))}for(let t of A(this,s).get(e.type))t(A(this,r),e)}}constructor(e){C(this,r,{}),C(this,s,new w(()=>new Set)),C(this,i,new Set),k(this,"disposables",_()),O(this,r,e)}}function R(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&I(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&I(e.entries(),t.entries()):!!(T(e)&&T(t))&&I(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function I(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function T(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function $(e,t){for(var n=arguments.length,r=Array(n>2?n-2:0),s=2;s<n;s++)r[s-2]=arguments[s];if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,$),i}r=new WeakMap,s=new WeakMap,i=new WeakMap;var j=Object.defineProperty,N=(e,t,n)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,M=(e,t,n)=>(N(e,"symbol"!=typeof t?t+"":t,n),n),L=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(L||{});let F={0(e,t){let n=t.id,r=e.stack,s=e.stack.indexOf(n);if(-1!==s){let t=e.stack.slice();return t.splice(s,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let s=e.stack.slice();return s.splice(r,1),{...e,stack:s}}};class D extends P{static new(){return new D({stack:[]})}reduce(e,t){return $(t.type,F,e,t)}constructor(){super(...arguments),M(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),M(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}}let W=new w(()=>D.new());var B=n(1992);let U=function(e){let t=y(e);return l.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])};function q(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:R;return(0,B.useSyncExternalStoreWithSelector)(U(t=>e.subscribe(H,t)),U(()=>e.state),U(()=>e.state),U(t),n)}function H(e){return e}function X(e,t){let n=(0,l.useId)(),r=W.get(t),[s,i]=q(r,(0,l.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return g(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!i||s)}function V(e){var t,n;return m.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let J=new Map,K=new Map;function Y(e){var t;let n=null!=(t=K.get(e))?t:0;return K.set(e,n+1),0!==n||(J.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=K.get(e))?t:1;if(1===n?K.delete(e):K.set(e,n-1),1!==n)return;let r=J.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,J.delete(e))})(e)}function z(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function G(e){return z(e)&&"tagName"in e}function Q(e){return G(e)&&"accessKey"in e}function Z(e){return G(e)&&"tabIndex"in e}let ee=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),et=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var en=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(en||{}),er=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(er||{}),es=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(es||{}),ei=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(ei||{}),eo=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(eo||{});function ea(e){null==e||e.focus({preventScroll:!0})}function el(e,t){var n,r,s;let{sorted:i=!0,relativeTo:o=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?i?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),s=t(n);if(null===r||null===s)return 0;let i=r.compareDocumentPosition(s);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(et)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(ee)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);a.length>0&&u.length>1&&(u=u.filter(e=>!a.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),o=null!=o?o:l.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,u.indexOf(o))-1;if(4&t)return Math.max(0,u.indexOf(o))+1;if(8&t)return u.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),h=32&t?{preventScroll:!0}:{},f=0,p=u.length,m;do{if(f>=p||f+p<=0)return 0;let e=d+f;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(m=u[e])||m.focus(h),f+=c}while(m!==l.activeElement);return 6&t&&null!=(s=null==(r=null==(n=m)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&s&&m.select(),2}function eu(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function ec(){return eu()||/Android/gi.test(window.navigator.userAgent)}function ed(e,t,n,r){let s=y(n);(0,l.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){s.current(e)}},[e,t,r])}function eh(e,t,n,r){let s=y(n);(0,l.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){s.current(e)}},[e,t,r])}function ef(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,l.useMemo)(()=>V(...t),[...t])}function ep(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var em=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(em||{}),eg=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(eg||{});function ey(){let e,t,n=(e=(0,l.useRef)([]),t=(0,l.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];if(!r.every(e=>null==e))return e.current=r,t});return(0,l.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:s,features:i,visible:o=!0,name:a,mergeRefs:l}=e;l=null!=l?l:ew;let u=eb(n,t);if(o)return ev(u,r,s,a,l);let c=null!=i?i:0;if(2&c){let{static:e=!1,...t}=u;if(e)return ev(t,r,s,a,l)}if(1&c){let{unmount:e=!0,...t}=u;return $(+!e,{0:()=>null,1:()=>ev({...t,hidden:!0,style:{display:"none"}},r,s,a,l)})}return ev(u,r,s,a,l)})({mergeRefs:n,...e}),[n])}function ev(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0,{as:i=n,children:o,refName:a="ref",...u}=eS(e,["unmount","static"]),c=void 0!==e.ref?{[a]:e.ref}:{},d="function"==typeof o?o(t):o;"className"in u&&u.className&&"function"==typeof u.className&&(u.className=u.className(t)),u["aria-labelledby"]&&u["aria-labelledby"]===u.id&&(u["aria-labelledby"]=void 0);let h={};if(t){let e=!1,n=[];for(let[r,s]of Object.entries(t))"boolean"==typeof s&&(e=!0),!0===s&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(h["data-headlessui-state"]=n.join(" "),n))h["data-".concat(e)]=""}if(i===l.Fragment&&(Object.keys(eE(u)).length>0||Object.keys(eE(h)).length>0))if(!(0,l.isValidElement)(d)||Array.isArray(d)&&d.length>1){if(Object.keys(eE(u)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(r,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(eE(u)).concat(Object.keys(eE(h))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var f;let e=d.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return ep(t(...n),u.className)}:ep(t,u.className),r=eb(d.props,eE(eS(u,["ref"])));for(let e in h)e in r&&delete h[e];return(0,l.cloneElement)(d,Object.assign({},r,h,c,{ref:s((f=d,l.version.split(".")[0]>="19"?f.props.ref:f.ref),c.ref)},n?{className:n}:{}))}return(0,l.createElement)(i,Object.assign({},eS(u,["ref"]),i!==l.Fragment&&c,i!==l.Fragment&&h),d)}function ew(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function eb(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},s={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=s[t]||(s[t]=[]),s[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in s)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(s[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in s)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(let n of s[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function e_(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function eE(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function eS(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}var ek=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ek||{});let ex=e_(function(e,t){var n;let{features:r=1,...s}=e,i={ref:t,"aria-hidden":(2&r)==2||(null!=(n=s["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return ey()({ourProps:i,theirProps:s,slot:{},defaultTag:"span",name:"Hidden"})}),eA=(0,l.createContext)(null);function eC(e){let{children:t,node:n}=e,[r,s]=(0,l.useState)(null),i=eO(null!=n?n:r);return l.createElement(eA.Provider,{value:i},t,null===i&&l.createElement(ex,{features:ek.Hidden,ref:e=>{var t,n;if(e){for(let r of null!=(n=null==(t=V(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&G(r)&&null!=r&&r.contains(e)){s(r);break}}}}))}function eO(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!=(e=(0,l.useContext)(eA))?e:t}let eP=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var s=arguments.length,i=Array(s>1?s-1:0),o=1;o<s;o++)i[o-1]=arguments[o];let a=t[e].call(n,...i);a&&(n=a,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:_(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:s}=e,i={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(s)},o=[eu()?{before(e){let{doc:t,d:n,meta:r}=e;function s(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=_();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,i=null;n.addEventListener(t,"click",e=>{if(Z(e.target))try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),o=t.querySelector(r);Z(o)&&!s(o)&&(i=o)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{var t;if(Z(e.target)&&G(t=e.target)&&"style"in t)if(s(e.target)){let t=e.target;for(;t.parentElement&&s(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(Z(e.target)){var t;if(!(Q(t=e.target)&&"INPUT"===t.nodeName))if(s(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{},{before(e){var n;let{doc:r}=e,s=r.documentElement;t=Math.max(0,(null!=(n=r.defaultView)?n:window).innerWidth-s.clientWidth)},after(e){let{doc:n,d:r}=e,s=n.documentElement,i=Math.max(0,s.clientWidth-s.offsetWidth),o=Math.max(0,t-i);r.style(s,"paddingRight","".concat(o,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];o.forEach(e=>{let{before:t}=e;return null==t?void 0:t(i)}),o.forEach(e=>{let{after:t}=e;return null==t?void 0:t(i)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});function eR(){let e,t=(e="undefined"==typeof document,(0,u.useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[n,r]=l.useState(m.isHandoffComplete);return n&&!1===m.isHandoffComplete&&r(!1),l.useEffect(()=>{!0!==n&&r(!0)},[n]),l.useEffect(()=>m.handoff(),[]),!t&&n}eP.subscribe(()=>{let e=eP.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&eP.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&eP.dispatch("TEARDOWN",n)}});let eI=Symbol();function eT(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=(0,l.useRef)(t);(0,l.useEffect)(()=>{r.current=t},[t]);let s=U(e=>{for(let t of r.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[eI]))?void 0:s}let e$=(0,l.createContext)(()=>{});function ej(e){let{value:t,children:n}=e;return l.createElement(e$.Provider,{value:t},n)}let eN=(0,l.createContext)(null);eN.displayName="OpenClosedContext";var eM=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(eM||{});function eL(){return(0,l.useContext)(eN)}function eF(e){let{value:t,children:n}=e;return l.createElement(eN.Provider,{value:t},n)}function eD(e){let{children:t}=e;return l.createElement(eN.Provider,{value:null},t)}let eW=(0,l.createContext)(!1);function eB(e){return l.createElement(eW.Provider,{value:e.force},e.children)}let eU=(0,l.createContext)(void 0),eq=(0,l.createContext)(null);eq.displayName="DescriptionContext";let eH=Object.assign(e_(function(e,t){let n=(0,l.useId)(),r=(0,l.useContext)(eU),{id:s="headlessui-description-".concat(n),...i}=e,o=function e(){let t=(0,l.useContext)(eq);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),a=eT(t);g(()=>o.register(s),[s,o.register]);let u=r||!1,c=(0,l.useMemo)(()=>({...o.slot,disabled:u}),[o.slot,u]),d={ref:a,...o.props,id:s};return ey()({ourProps:d,theirProps:i,slot:c,defaultTag:"p",name:o.name||"Description"})}),{});function eX(){let[e]=(0,l.useState)(_);return(0,l.useEffect)(()=>()=>e.dispose(),[e]),e}function eV(){let e=(0,l.useRef)(!1);return g(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function eJ(e){let t=U(e),n=(0,l.useRef)(!1);(0,l.useEffect)(()=>(n.current=!1,()=>{n.current=!0,b(()=>{n.current&&t()})}),[t])}var eK=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(eK||{});function eY(e,t){let n=(0,l.useRef)([]),r=U(e);(0,l.useEffect)(()=>{let e=[...n.current];for(let[s,i]of t.entries())if(n.current[s]!==i){let s=r(t,e);return n.current=t,s}},[r,...t])}let ez=[];function eG(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)G(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!Z(e.target)||e.target===document.body||ez[0]===e.target)return;let t=e.target;t=t.closest(ee),ez.unshift(null!=t?t:e.target),(ez=ez.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var eQ=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(eQ||{});let eZ=Object.assign(e_(function(e,t){let n,r=(0,l.useRef)(null),s=eT(r,t),{initialFocus:i,initialFocusFallback:o,containers:a,features:u=15,...c}=e;eR()||(u=0);let d=ef(r);!function(e,t){let{ownerDocument:n}=t,r=!!(8&e),s=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,l.useRef)(ez.slice());return eY((e,n)=>{let[r]=e,[s]=n;!0===s&&!1===r&&b(()=>{t.current.splice(0)}),!1===s&&!0===r&&(t.current=ez.slice())},[e,ez,t]),U(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);eY(()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&ea(s())},[r]),eJ(()=>{r&&ea(s())})}(u,{ownerDocument:d});let h=function(e,t){let{ownerDocument:n,container:r,initialFocus:s,initialFocusFallback:i}=t,o=(0,l.useRef)(null),a=X(!!(1&e),"focus-trap#initial-focus"),u=eV();return eY(()=>{if(0===e)return;if(!a){null!=i&&i.current&&ea(i.current);return}let t=r.current;t&&b(()=>{if(!u.current)return;let r=null==n?void 0:n.activeElement;if(null!=s&&s.current){if((null==s?void 0:s.current)===r){o.current=r;return}}else if(t.contains(r)){o.current=r;return}if(null!=s&&s.current)ea(s.current);else{if(16&e){if(el(t,en.First|en.AutoFocus)!==er.Error)return}else if(el(t,en.First)!==er.Error)return;if(null!=i&&i.current&&(ea(i.current),(null==n?void 0:n.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}o.current=null==n?void 0:n.activeElement})},[i,a,e]),o}(u,{ownerDocument:d,container:r,initialFocus:i,initialFocusFallback:o});!function(e,t){let{ownerDocument:n,container:r,containers:s,previousActiveElement:i}=t,o=eV(),a=!!(4&e);v(null==n?void 0:n.defaultView,"focus",e=>{if(!a||!o.current)return;let t=eG(s);Q(r.current)&&t.add(r.current);let n=i.current;if(!n)return;let l=e.target;Q(l)?e0(t,l)?(i.current=l,ea(l)):(e.preventDefault(),e.stopPropagation(),ea(n)):ea(i.current)},!0)}(u,{ownerDocument:d,container:r,containers:a,previousActiveElement:h});let f=(n=(0,l.useRef)(0),eh(!0,"keydown",e=>{"Tab"===e.key&&(n.current=+!!e.shiftKey)},!0),n),p=U(e=>{if(!Q(r.current))return;let t=r.current;$(f.current,{[eK.Forwards]:()=>{el(t,en.First,{skipElements:[e.relatedTarget,o]})},[eK.Backwards]:()=>{el(t,en.Last,{skipElements:[e.relatedTarget,o]})}})}),m=X(!!(2&u),"focus-trap#tab-lock"),g=eX(),y=(0,l.useRef)(!1),w=ey();return l.createElement(l.Fragment,null,m&&l.createElement(ex,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:ek.Focusable}),w({ourProps:{ref:s,onKeyDown(e){"Tab"==e.key&&(y.current=!0,g.requestAnimationFrame(()=>{y.current=!1}))},onBlur(e){if(!(4&u))return;let t=eG(a);Q(r.current)&&t.add(r.current);let n=e.relatedTarget;Z(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(e0(t,n)||(y.current?el(r.current,$(f.current,{[eK.Forwards]:()=>en.Next,[eK.Backwards]:()=>en.Previous})|en.WrapAround,{relativeTo:e.target}):Z(e.target)&&ea(e.target)))}},theirProps:c,defaultTag:"div",name:"FocusTrap"}),m&&l.createElement(ex,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:ek.Focusable}))}),{features:eQ});function e0(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var e1=n(7650);let e2=l.Fragment,e4=e_(function(e,t){let{ownerDocument:n=null,...r}=e,s=(0,l.useRef)(null),i=eT(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[eI]:t})}(e=>{s.current=e}),t),o=ef(s),a=null!=n?n:o,u=function(e){let t=(0,l.useContext)(eW),n=(0,l.useContext)(e5),[r,s]=(0,l.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(m.isServer)return null;let s=null==e?void 0:e.getElementById("headlessui-portal-root");if(s)return s;if(null===e)return null;let i=e.createElement("div");return i.setAttribute("id","headlessui-portal-root"),e.body.appendChild(i)});return(0,l.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,l.useEffect)(()=>{t||null!==n&&s(n.current)},[n,s,t]),r}(a),[c]=(0,l.useState)(()=>{var e;return m.isServer?null:null!=(e=null==a?void 0:a.createElement("div"))?e:null}),d=(0,l.useContext)(e9),h=eR();g(()=>{!u||!c||u.contains(c)||(c.setAttribute("data-headlessui-portal",""),u.appendChild(c))},[u,c]),g(()=>{if(c&&d)return d.register(c)},[d,c]),eJ(()=>{var e;u&&c&&(z(c)&&u.contains(c)&&u.removeChild(c),u.childNodes.length<=0&&(null==(e=u.parentElement)||e.removeChild(u)))});let f=ey();return h&&u&&c?(0,e1.createPortal)(f({ourProps:{ref:i},theirProps:r,slot:{},defaultTag:e2,name:"Portal"}),c):null}),e3=l.Fragment,e5=(0,l.createContext)(null),e9=(0,l.createContext)(null),e6=e_(function(e,t){let n=eT(t),{enabled:r=!0,ownerDocument:s,...i}=e,o=ey();return r?l.createElement(e4,{...i,ownerDocument:s,ref:n}):o({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:e2,name:"Portal"})}),e8=e_(function(e,t){let{target:n,...r}=e,s={ref:eT(t)},i=ey();return l.createElement(e5.Provider,{value:n},i({ourProps:s,theirProps:r,defaultTag:e3,name:"Popover.Group"}))}),e7=Object.assign(e6,{Group:e8});var te=n(9509);void 0!==te&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(o=null==te?void 0:te.env)?void 0:o.NODE_ENV)==="test"&&void 0===(null==(a=null==Element?void 0:Element.prototype)?void 0:a.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var tt=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(tt||{});function tn(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:tl)!==l.Fragment||1===l.Children.count(e.children)}let tr=(0,l.createContext)(null);tr.displayName="TransitionContext";var ts=(e=>(e.Visible="visible",e.Hidden="hidden",e))(ts||{});let ti=(0,l.createContext)(null);function to(e){return"children"in e?to(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function ta(e,t){let n=y(e),r=(0,l.useRef)([]),s=eV(),i=eX(),o=U(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eg.Hidden,o=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==o&&($(t,{[eg.Unmount](){r.current.splice(o,1)},[eg.Hidden](){r.current[o].state="hidden"}}),i.microTask(()=>{var e;!to(r)&&s.current&&(null==(e=n.current)||e.call(n))}))}),a=U(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>o(e,eg.Unmount)}),u=(0,l.useRef)([]),c=(0,l.useRef)(Promise.resolve()),d=(0,l.useRef)({enter:[],leave:[]}),h=U((e,n,r)=>{u.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{u.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(d.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?c.current=c.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),f=U((e,t,n)=>{Promise.all(d.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=u.current.shift())||e()}).then(()=>n(t))});return(0,l.useMemo)(()=>({children:r,register:a,unregister:o,onStart:h,onStop:f,wait:c,chains:d}),[a,o,r,h,f,d,c])}ti.displayName="NestingContext";let tl=l.Fragment,tu=em.RenderStrategy,tc=e_(function(e,t){let{show:n,appear:r=!1,unmount:s=!0,...i}=e,o=(0,l.useRef)(null),a=eT(...tn(e)?[o,t]:null===t?[]:[t]);eR();let u=eL();if(void 0===n&&null!==u&&(n=(u&eM.Open)===eM.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,d]=(0,l.useState)(n?"visible":"hidden"),h=ta(()=>{n||d("hidden")}),[f,p]=(0,l.useState)(!0),m=(0,l.useRef)([n]);g(()=>{!1!==f&&m.current[m.current.length-1]!==n&&(m.current.push(n),p(!1))},[m,n]);let y=(0,l.useMemo)(()=>({show:n,appear:r,initial:f}),[n,r,f]);g(()=>{n?d("visible"):to(h)||null===o.current||d("hidden")},[n,h]);let v={unmount:s},w=U(()=>{var t;f&&p(!1),null==(t=e.beforeEnter)||t.call(e)}),b=U(()=>{var t;f&&p(!1),null==(t=e.beforeLeave)||t.call(e)}),_=ey();return l.createElement(ti.Provider,{value:h},l.createElement(tr.Provider,{value:y},_({ourProps:{...v,as:l.Fragment,children:l.createElement(td,{ref:a,...v,...i,beforeEnter:w,beforeLeave:b})},theirProps:{},defaultTag:l.Fragment,features:tu,visible:"visible"===c,name:"Transition"})))}),td=e_(function(e,t){var n,r;let{transition:s=!0,beforeEnter:i,afterEnter:o,beforeLeave:a,afterLeave:u,enter:c,enterFrom:d,enterTo:h,entered:f,leave:p,leaveFrom:m,leaveTo:y,...v}=e,[w,b]=(0,l.useState)(null),E=(0,l.useRef)(null),S=tn(e),k=eT(...S?[E,t,b]:null===t?[]:[t]),x=null==(n=v.unmount)||n?eg.Unmount:eg.Hidden,{show:A,appear:C,initial:O}=function(){let e=(0,l.useContext)(tr);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[P,R]=(0,l.useState)(A?"visible":"hidden"),I=function(){let e=(0,l.useContext)(ti);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:T,unregister:j}=I;g(()=>T(E),[T,E]),g(()=>{if(x===eg.Hidden&&E.current)return A&&"visible"!==P?void R("visible"):$(P,{hidden:()=>j(E),visible:()=>T(E)})},[P,E,T,j,A,x]);let N=eR();g(()=>{if(S&&N&&"visible"===P&&null===E.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[E,P,N,S]);let M=O&&!C,L=C&&A&&O,F=(0,l.useRef)(!1),D=ta(()=>{F.current||(R("hidden"),j(E))},I),W=U(e=>{F.current=!0,D.onStart(E,e?"enter":"leave",e=>{"enter"===e?null==i||i():"leave"===e&&(null==a||a())})}),B=U(e=>{let t=e?"enter":"leave";F.current=!1,D.onStop(E,t,e=>{"enter"===e?null==o||o():"leave"===e&&(null==u||u())}),"leave"!==t||to(D)||(R("hidden"),j(E))});(0,l.useEffect)(()=>{S&&s||(W(A),B(A))},[A,S,s]);let[,q]=function(e,t,n,r){let[s,i]=(0,l.useState)(n),{hasFlag:o,addFlag:a,removeFlag:u}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,l.useState)(e),r=(0,l.useCallback)(e=>n(e),[t]),s=(0,l.useCallback)(e=>n(t=>t|e),[t]),i=(0,l.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:s,hasFlag:i,removeFlag:(0,l.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,l.useCallback)(e=>n(t=>t^e),[n])}}(e&&s?3:0),c=(0,l.useRef)(!1),d=(0,l.useRef)(!1);return g(()=>{var s;if(e){if(n&&i(!0),!t){n&&a(3);return}return null==(s=null==r?void 0:r.start)||s.call(r,n),function(e,t){let{prepare:n,run:r,done:s,inFlight:i}=t,o=_();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let s=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=s}(e,{prepare:n,inFlight:i}),o.nextFrame(()=>{r(),o.requestAnimationFrame(()=>{o.add(function(e,t){var n,r;let s=_();if(!e)return s.dispose;let i=!1;s.add(()=>{i=!0});let o=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===o.length?t():Promise.allSettled(o.map(e=>e.finished)).then(()=>{i||t()}),s.dispose}(e,s))})}),o.dispose}(t,{inFlight:c,prepare(){d.current?d.current=!1:d.current=c.current,c.current=!0,d.current||(n?(a(3),u(4)):(a(4),u(2)))},run(){d.current?n?(u(3),a(4)):(u(4),a(3)):n?u(1):a(1)},done(){var e;d.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(c.current=!1,u(7),n||i(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,eX()]),e?[s,{closed:o(1),enter:o(2),leave:o(4),transition:o(2)||o(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!s||!S||!N||M),w,A,{start:W,end:B}),H=eE({ref:k,className:(null==(r=ep(v.className,L&&c,L&&d,q.enter&&c,q.enter&&q.closed&&d,q.enter&&!q.closed&&h,q.leave&&p,q.leave&&!q.closed&&m,q.leave&&q.closed&&y,!q.transition&&A&&f))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(q)}),X=0;"visible"===P&&(X|=eM.Open),"hidden"===P&&(X|=eM.Closed),A&&"hidden"===P&&(X|=eM.Opening),A||"visible"!==P||(X|=eM.Closing);let V=ey();return l.createElement(ti.Provider,{value:D},l.createElement(eF,{value:X},V({ourProps:H,theirProps:v,defaultTag:tl,features:tu,visible:"visible"===P,name:"Transition.Child"})))}),th=e_(function(e,t){let n=null!==(0,l.useContext)(tr),r=null!==eL();return l.createElement(l.Fragment,null,!n&&r?l.createElement(tc,{ref:t,...e}):l.createElement(td,{ref:t,...e}))}),tf=Object.assign(tc,{Child:th,Root:tc});var tp=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(tp||{}),tm=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(tm||{});let tg={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},ty=(0,l.createContext)(null);function tv(e){let t=(0,l.useContext)(ty);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,tv),t}return t}function tw(e,t){return $(t.type,tg,e,t)}ty.displayName="DialogContext";let tb=e_(function(e,t){let n,r,s,i,o,a,u,d,h,f,p=(0,l.useId)(),{id:m="headlessui-dialog-".concat(p),open:w,onClose:b,initialFocus:E,role:S="dialog",autoFocus:k=!0,__demoMode:x=!1,unmount:A=!1,...C}=e,O=(0,l.useRef)(!1);S="dialog"===S||"alertdialog"===S?S:(O.current||(O.current=!0,console.warn("Invalid role [".concat(S,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let P=eL();void 0===w&&null!==P&&(w=(P&eM.Open)===eM.Open);let R=(0,l.useRef)(null),I=eT(R,t),T=ef(R),j=+!w,[N,M]=(0,l.useReducer)(tw,{titleId:null,descriptionId:null,panelRef:(0,l.createRef)()}),L=U(()=>b(!1)),F=U(e=>M({type:0,id:e})),D=!!eR()&&0===j,[B,H]=(n=(0,l.useContext)(e9),r=(0,l.useRef)([]),s=U(e=>(r.current.push(e),n&&n.register(e),()=>i(e))),i=U(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),o=(0,l.useMemo)(()=>({register:s,unregister:i,portals:r}),[s,i,r]),[r,(0,l.useMemo)(()=>function(e){let{children:t}=e;return l.createElement(e9.Provider,{value:o},t)},[o])]),J=eO(),{resolveContainers:K}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=ef(n),s=U(()=>{var s,i;let o=[];for(let t of e)null!==t&&(G(t)?o.push(t):"current"in t&&G(t.current)&&o.push(t.current));if(null!=t&&t.current)for(let e of t.current)o.push(e);for(let e of null!=(s=null==r?void 0:r.querySelectorAll("html > *, body > *"))?s:[])e!==document.body&&e!==document.head&&G(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(i=null==n?void 0:n.getRootNode())?void 0:i.host))||o.some(t=>e.contains(t))||o.push(e));return o});return{resolveContainers:s,contains:U(e=>s().some(t=>t.contains(e)))}}({mainTreeNode:J,portals:B,defaultContainers:[{get current(){var z;return null!=(z=N.panelRef.current)?z:R.current}}]}),et=null!==P&&(P&eM.Closing)===eM.Closing;!function(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=X(e,"inert-others");g(()=>{var e,s;if(!r)return;let i=_();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&i.add(Y(t));let o=null!=(s=null==t?void 0:t())?s:[];for(let e of o){if(!e)continue;let t=V(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)o.some(t=>e.contains(t))||i.add(Y(e));n=n.parentElement}}return i.dispose},[r,t,n])}(!x&&!et&&D,{allowed:U(()=>{var e,t;return[null!=(t=null==(e=R.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:U(()=>{var e;return[null!=(e=null==J?void 0:J.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let en=W.get(null);g(()=>{if(D)return en.actions.push(m),()=>en.actions.pop(m)},[en,m,D]);let er=q(en,(0,l.useCallback)(e=>en.selectors.isTop(e,m),[en,m]));a=y(e=>{e.preventDefault(),L()}),u=(0,l.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(K))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=V(e))?void 0:t.body)&&$(n,{0:()=>e.matches(ee),1(){let t=e;for(;null!==t;){if(t.matches(ee))return!0;t=t.parentElement}return!1}})}(n,ei.Loose)||-1===n.tabIndex||e.preventDefault(),a.current(e,n)}},[a,K]),d=(0,l.useRef)(null),ed(er,"pointerdown",e=>{var t,n;ec()||(d.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),ed(er,"pointerup",e=>{if(ec()||!d.current)return;let t=d.current;return d.current=null,u(e,()=>t)},!0),h=(0,l.useRef)({x:0,y:0}),ed(er,"touchstart",e=>{h.current.x=e.touches[0].clientX,h.current.y=e.touches[0].clientY},!0),ed(er,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-h.current.x)>=30||Math.abs(t.y-h.current.y)>=30))return u(e,()=>Z(e.target)?e.target:null)},!0),eh(er,"blur",e=>u(e,()=>{var e;return Q(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}),!0),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=X(e,"escape");v(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===c.Escape&&n(e))})}(er,null==T?void 0:T.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),L()}),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=(0,l.useSyncExternalStore)(eP.subscribe,eP.getSnapshot,eP.getSnapshot),s=t?r.get(t):void 0;s&&s.count,g(()=>{if(!(!t||!e))return eP.dispatch("PUSH",t,n),()=>eP.dispatch("POP",t,n)},[e,t])}(X(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!x&&!et&&D,T,K),f=y(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&L()}),(0,l.useEffect)(()=>{if(!D)return;let e=null===R?null:Q(R)?R:R.current;if(!e)return;let t=_();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>f.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>f.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[R,f,D]);let[es,eo]=function(){let[e,t]=(0,l.useState)([]);return[e.length>0?e.join(" "):void 0,(0,l.useMemo)(()=>function(e){let n=U(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,l.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return l.createElement(eq.Provider,{value:r},e.children)},[t])]}(),ea=(0,l.useMemo)(()=>[{dialogState:j,close:L,setTitleId:F,unmount:A},N],[j,N,L,F,A]),el=(0,l.useMemo)(()=>({open:0===j}),[j]),eu={ref:I,id:m,role:S,tabIndex:-1,"aria-modal":x?void 0:0===j||void 0,"aria-labelledby":N.titleId,"aria-describedby":es,unmount:A},ep=!function(){var e;let[t]=(0,l.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,l.useState)(null!=(e=null==t?void 0:t.matches)&&e);return g(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),em=eQ.None;D&&!x&&(em|=eQ.RestoreFocus,em|=eQ.TabLock,k&&(em|=eQ.AutoFocus),ep&&(em|=eQ.InitialFocus));let eg=ey();return l.createElement(eD,null,l.createElement(eB,{force:!0},l.createElement(e7,null,l.createElement(ty.Provider,{value:ea},l.createElement(e8,{target:R},l.createElement(eB,{force:!1},l.createElement(eo,{slot:el},l.createElement(H,null,l.createElement(eZ,{initialFocus:E,initialFocusFallback:R,containers:K,features:em},l.createElement(ej,{value:L},eg({ourProps:eu,theirProps:C,slot:el,defaultTag:t_,features:tE,visible:0===j,name:"Dialog"})))))))))))}),t_="div",tE=em.RenderStrategy|em.Static,tS=Object.assign(e_(function(e,t){let{transition:n=!1,open:r,...s}=e,i=eL(),o=e.hasOwnProperty("open")||null!==i,a=e.hasOwnProperty("onClose");if(!o&&!a)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!o)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!a)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return(void 0!==r||n)&&!s.static?l.createElement(eC,null,l.createElement(tf,{show:r,transition:n,unmount:s.unmount},l.createElement(tb,{ref:t,...s}))):l.createElement(eC,null,l.createElement(tb,{ref:t,open:r,...s}))}),{Panel:e_(function(e,t){let n=(0,l.useId)(),{id:r="headlessui-dialog-panel-".concat(n),transition:s=!1,...i}=e,[{dialogState:o,unmount:a},u]=tv("Dialog.Panel"),c=eT(t,u.panelRef),d=(0,l.useMemo)(()=>({open:0===o}),[o]),h=U(e=>{e.stopPropagation()}),f=s?th:l.Fragment,p=ey();return l.createElement(f,{...s?{unmount:a}:{}},p({ourProps:{ref:c,id:r,onClick:h},theirProps:i,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:(e_(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:s,unmount:i}]=tv("Dialog.Backdrop"),o=(0,l.useMemo)(()=>({open:0===s}),[s]),a=n?th:l.Fragment,u=ey();return l.createElement(a,{...n?{unmount:i}:{}},u({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:o,defaultTag:"div",name:"Dialog.Backdrop"}))}),e_(function(e,t){let n=(0,l.useId)(),{id:r="headlessui-dialog-title-".concat(n),...s}=e,[{dialogState:i,setTitleId:o}]=tv("Dialog.Title"),a=eT(t);(0,l.useEffect)(()=>(o(r),()=>o(null)),[r,o]);let u=(0,l.useMemo)(()=>({open:0===i}),[i]);return ey()({ourProps:{ref:a,id:r},theirProps:s,slot:u,defaultTag:"h2",name:"Dialog.Title"})})),Description:eH})},381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1586:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1992:(e,t,n)=>{"use strict";e.exports=n(4993)},2355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2919:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3052:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4186:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4835:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},4993:(e,t,n)=>{"use strict";var r=n(2115),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useSyncExternalStore,o=r.useRef,a=r.useEffect,l=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=o(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;var f=i(e,(d=l(function(){function e(e){if(!a){if(a=!0,i=e,e=r(e),void 0!==c&&h.hasValue){var t=h.value;if(c(t,e))return o=t}return o=e}if(t=o,s(i,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(i=e,t):(i=e,o=n)}var i,o,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,r,c]))[0],d[1]);return a(function(){h.hasValue=!0,h.value=f},[f]),u(f),f}},5196:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5339:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5855:function(e,t,n){(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){if(null==e)return{};var n,r,s=function(e,t){if(null==e)return{};var n,r,s={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(s[n]=e[n]);return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,s=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=s){var i=[],o=!0,a=!1;try{for(s=s.call(e);!(o=(n=s.next()).done)&&(i.push(n.value),!t||i.length!==t);o=!0);}catch(e){a=!0,r=e}finally{try{o||null==s.return||s.return()}finally{if(a)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var u,c,d,h,f,p={exports:{}};p.exports=(function(){if(f)return h;f=1;var e=d?c:(d=1,c="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,h=function(){function r(t,n,r,s,i,o){if(o!==e){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function s(){return r}r.isRequired=r;var i={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:s,element:r,elementType:r,instanceOf:s,node:r,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:n,resetWarningCache:t};return i.PropTypes=i,i}})()();var m=(u=p.exports)&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u,g=function(e,n,r){var s=!!r,i=t.useRef(r);t.useEffect(function(){i.current=r},[r]),t.useEffect(function(){if(!s||!e)return function(){};var t=function(){i.current&&i.current.apply(i,arguments)};return e.on(n,t),function(){e.off(n,t)}},[s,n,e,i])},y=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},v=function(e){return null!==e&&"object"===s(e)},w="[object Object]",b=function e(t,n){if(!v(t)||!v(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var s=Object.prototype.toString.call(t)===w;if(s!==(Object.prototype.toString.call(n)===w))return!1;if(!s&&!r)return t===n;var i=Object.keys(t),o=Object.keys(n);if(i.length!==o.length)return!1;for(var a={},l=0;l<i.length;l+=1)a[i[l]]=!0;for(var u=0;u<o.length;u+=1)a[o[u]]=!0;var c=Object.keys(a);return c.length===i.length&&c.every(function(r){return e(t[r],n[r])})},_=function(e,t,n){return v(e)?Object.keys(e).reduce(function(s,o){var a=!v(t)||!b(e[o],t[o]);return n.includes(o)?(a&&console.warn("Unsupported prop change: options.".concat(o," is not a mutable property.")),s):a?r(r({},s||{}),{},i({},o,e[o])):s},null):null},E="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:E;if(null===e||v(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},k=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:E;if(v(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return S(e,t)})};var n=S(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},x=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},A=t.createContext(null);A.displayName="ElementsContext";var C=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},O=function(e){var n=e.stripe,r=e.options,s=e.children,i=t.useMemo(function(){return k(n)},[n]),o=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,elements:"sync"===i.tag?i.stripe.elements(r):null}}),2),l=o[0],u=o[1];t.useEffect(function(){var e=!0,t=function(e){u(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==i.tag||l.stripe?"sync"!==i.tag||l.stripe||t(i.stripe):i.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[i,l,r]);var c=y(n);t.useEffect(function(){null!==c&&c!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[c,n]);var d=y(r);return t.useEffect(function(){if(l.elements){var e=_(r,d,["clientSecret","fonts"]);e&&l.elements.update(e)}},[r,d,l.elements]),t.useEffect(function(){x(l.stripe)},[l.stripe]),t.createElement(A.Provider,{value:l},s)};O.propTypes={stripe:m.any,options:m.object};var P=function(e){return C(t.useContext(A),e)},R=function(e){return(0,e.children)(P("mounts <ElementsConsumer>"))};R.propTypes={children:m.func.isRequired};var I=["on","session"],T=t.createContext(null);T.displayName="CheckoutSdkContext";var $=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},j=t.createContext(null);j.displayName="CheckoutContext";var N=function(e,t){if(!e)return null;e.on,e.session;var n=o(e,I);return t?Object.assign(t,n):Object.assign(e.session(),n)},M=function(e){var n=e.stripe,r=e.options,s=e.children,i=t.useMemo(function(){return k(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),o=a(t.useState(null),2),l=o[0],u=o[1],c=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,checkoutSdk:null}}),2),d=c[0],h=c[1],f=function(e,t){h(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},p=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==i.tag||d.stripe?"sync"===i.tag&&i.stripe&&!p.current&&(p.current=!0,i.stripe.initCheckout(r).then(function(e){e&&(f(i.stripe,e),e.on("change",u))})):i.stripePromise.then(function(t){t&&e&&!p.current&&(p.current=!0,t.initCheckout(r).then(function(e){e&&(f(t,e),e.on("change",u))}))}),function(){e=!1}},[i,d,r,u]);var m=y(n);t.useEffect(function(){null!==m&&m!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[m,n]);var g=y(r),v=y(d.checkoutSdk);t.useEffect(function(){if(d.checkoutSdk){var e,t,n=null==g||null==(e=g.elementsOptions)?void 0:e.appearance,s=null==r||null==(t=r.elementsOptions)?void 0:t.appearance,i=!b(s,n),o=!v&&d.checkoutSdk;s&&(i||o)&&d.checkoutSdk.changeAppearance(s)}},[r,g,d.checkoutSdk,v]),t.useEffect(function(){x(d.stripe)},[d.stripe]);var w=t.useMemo(function(){return N(d.checkoutSdk,l)},[d.checkoutSdk,l]);return d.checkoutSdk?t.createElement(T.Provider,{value:d},t.createElement(j.Provider,{value:w},s)):null};M.propTypes={stripe:m.any,options:m.shape({fetchClientSecret:m.func.isRequired,elementsOptions:m.object}).isRequired};var L=function(e){var n=t.useContext(T),r=t.useContext(A);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?$(n,e):C(r,e)},F=["mode"],D=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),s=n?function(e){L("mounts <".concat(r,">"));var n=e.id,s=e.className;return t.createElement("div",{id:n,className:s})}:function(n){var s,i=n.id,l=n.className,u=n.options,c=void 0===u?{}:u,d=n.onBlur,h=n.onFocus,f=n.onReady,p=n.onChange,m=n.onEscape,v=n.onClick,w=n.onLoadError,b=n.onLoaderStart,E=n.onNetworksChange,S=n.onConfirm,k=n.onCancel,x=n.onShippingAddressChange,A=n.onShippingRateChange,C=L("mounts <".concat(r,">")),O="elements"in C?C.elements:null,P="checkoutSdk"in C?C.checkoutSdk:null,R=a(t.useState(null),2),I=R[0],T=R[1],$=t.useRef(null),j=t.useRef(null);g(I,"blur",d),g(I,"focus",h),g(I,"escape",m),g(I,"click",v),g(I,"loaderror",w),g(I,"loaderstart",b),g(I,"networkschange",E),g(I,"confirm",S),g(I,"cancel",k),g(I,"shippingaddresschange",x),g(I,"shippingratechange",A),g(I,"change",p),f&&(s="expressCheckout"===e?f:function(){f(I)}),g(I,"ready",s),t.useLayoutEffect(function(){if(null===$.current&&null!==j.current&&(O||P)){var t=null;if(P)switch(e){case"payment":t=P.createPaymentElement(c);break;case"address":if("mode"in c){var n=c.mode,s=o(c,F);if("shipping"===n)t=P.createShippingAddressElement(s);else if("billing"===n)t=P.createBillingAddressElement(s);else throw Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":t=P.createExpressCheckoutElement(c);break;case"currencySelector":t=P.createCurrencySelectorElement();break;default:throw Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else O&&(t=O.create(e,c));$.current=t,T(t),t&&t.mount(j.current)}},[O,P,c]);var N=y(c);return t.useEffect(function(){if($.current){var e=_(c,N,["paymentRequest"]);e&&"update"in $.current&&$.current.update(e)}},[c,N]),t.useLayoutEffect(function(){return function(){if($.current&&"function"==typeof $.current.destroy)try{$.current.destroy(),$.current=null}catch(e){}}},[]),t.createElement("div",{id:i,className:l,ref:j})};return s.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},s.displayName=r,s.__elementType=e,s},W="undefined"==typeof window,B=t.createContext(null);B.displayName="EmbeddedCheckoutProviderContext";var U=function(){var e=t.useContext(B);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},q=W?function(e){var n=e.id,r=e.className;return U(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,s=U().embeddedCheckout,i=t.useRef(!1),o=t.useRef(null);return t.useLayoutEffect(function(){return!i.current&&s&&null!==o.current&&(s.mount(o.current),i.current=!0),function(){if(i.current&&s)try{s.unmount(),i.current=!1}catch(e){}}},[s]),t.createElement("div",{ref:o,id:n,className:r})},H=D("auBankAccount",W),X=D("card",W),V=D("cardNumber",W),J=D("cardExpiry",W),K=D("cardCvc",W),Y=D("fpxBank",W),z=D("iban",W),G=D("idealBank",W),Q=D("p24Bank",W),Z=D("epsBank",W),ee=D("payment",W),et=D("expressCheckout",W),en=D("currencySelector",W),er=D("paymentRequestButton",W),es=D("linkAuthentication",W),ei=D("address",W),eo=D("shippingAddress",W),ea=D("paymentMethodMessaging",W),el=D("affirmMessage",W),eu=D("afterpayClearpayMessage",W);e.AddressElement=ei,e.AffirmMessageElement=el,e.AfterpayClearpayMessageElement=eu,e.AuBankAccountElement=H,e.CardCvcElement=K,e.CardElement=X,e.CardExpiryElement=J,e.CardNumberElement=V,e.CheckoutProvider=M,e.CurrencySelectorElement=en,e.Elements=O,e.ElementsConsumer=R,e.EmbeddedCheckout=q,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,s=e.children,i=t.useMemo(function(){return k(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),o=t.useRef(null),l=t.useRef(null),u=a(t.useState({embeddedCheckout:null}),2),c=u[0],d=u[1];t.useEffect(function(){if(!l.current&&!o.current){var e=function(e){l.current||o.current||(l.current=e,o.current=l.current.initEmbeddedCheckout(r).then(function(e){d({embeddedCheckout:e})}))};"async"===i.tag&&!l.current&&(r.clientSecret||r.fetchClientSecret)?i.stripePromise.then(function(t){t&&e(t)}):"sync"===i.tag&&!l.current&&(r.clientSecret||r.fetchClientSecret)&&e(i.stripe)}},[i,r,c,l]),t.useEffect(function(){return function(){c.embeddedCheckout?(o.current=null,c.embeddedCheckout.destroy()):o.current&&o.current.then(function(){o.current=null,c.embeddedCheckout&&c.embeddedCheckout.destroy()})}},[c.embeddedCheckout]),t.useEffect(function(){x(l)},[l]);var h=y(n);t.useEffect(function(){null!==h&&h!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[h,n]);var f=y(r);return t.useEffect(function(){if(null!=f){if(null==r)return void console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=f.clientSecret&&r.clientSecret!==f.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.fetchClientSecret&&r.fetchClientSecret!==f.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.onComplete&&r.onComplete!==f.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=f.onShippingDetailsChange&&r.onShippingDetailsChange!==f.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=f.onLineItemsChange&&r.onLineItemsChange!==f.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[f,r]),t.createElement(B.Provider,{value:c},s)},e.EpsBankElement=Z,e.ExpressCheckoutElement=et,e.FpxBankElement=Y,e.IbanElement=z,e.IdealBankElement=G,e.LinkAuthenticationElement=es,e.P24BankElement=Q,e.PaymentElement=ee,e.PaymentMethodMessagingElement=ea,e.PaymentRequestButtonElement=er,e.ShippingAddressElement=eo,e.useCheckout=function(){$(t.useContext(T),"calls useCheckout()");var e=t.useContext(j);if(!e)throw Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return e},e.useElements=function(){return P("calls useElements()").elements},e.useStripe=function(){return L("calls useStripe()").stripe}})(t,n(2115))},7368:(e,t,n)=>{"use strict";n.d(t,{c:()=>v});var r,s="basil",i="https://js.stripe.com",o="".concat(i,"/").concat(s,"/stripe.js"),a=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,u=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),t=0;t<e.length;t++){var n,r=e[t];if(n=r.src,a.test(n)||l.test(n))return r}return null},c=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(o).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},d=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.4.0",startTime:t})},h=null,f=null,p=null,m=function(e,t,n){if(null===e)return null;var r,i=t[0].match(/^pk_test/),o=3===(r=e.version)?"v3":r;i&&o!==s&&console.warn("Stripe.js@".concat(o," was loaded on the page, but @stripe/stripe-js@").concat("7.4.0"," expected Stripe.js@").concat(s,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var a=e.apply(void 0,t);return d(a,n),a},g=!1,y=function(){return r?r:r=(null!==h?h:(h=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document)return void e(null);if(window.Stripe,window.Stripe)return void e(window.Stripe);try{var n,r=u();r?r&&null!==p&&null!==f&&(r.removeEventListener("load",p),r.removeEventListener("error",f),null==(n=r.parentNode)||n.removeChild(r),r=c(null)):r=c(null),p=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},f=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},r.addEventListener("load",p),r.addEventListener("error",f)}catch(e){t(e);return}})).catch(function(e){return h=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)})};Promise.resolve().then(function(){return y()}).catch(function(e){g||console.warn(e)});var v=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];g=!0;var r=Date.now();return y().then(function(e){return m(e,t,r)})}},8883:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9617:(e,t,n)=>{"use strict";let r,s,i,o;function a(e,t,n,r,s){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?s.call(e,n):s?s.value=n:t.set(e,n),n}function l(e,t,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}n.d(t,{Ay:()=>rs});let u=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return u=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),n=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^n()&15>>e/4).toString(16))};function c(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let d=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class h extends Error{}class f extends h{constructor(e,t,n,r){super(`${f.makeMessage(e,t,n)}`),this.status=e,this.headers=r,this.requestID=r?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,n){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):n;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,n,r){if(!e||!r)return new m({message:n,cause:d(t)});let s=t?.error;return 400===e?new y(e,s,n,r):401===e?new v(e,s,n,r):403===e?new w(e,s,n,r):404===e?new b(e,s,n,r):409===e?new _(e,s,n,r):422===e?new E(e,s,n,r):429===e?new S(e,s,n,r):e>=500?new k(e,s,n,r):new f(e,s,n,r)}}class p extends f{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class m extends f{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class g extends m{constructor({message:e}={}){super({message:e??"Request timed out."})}}class y extends f{}class v extends f{}class w extends f{}class b extends f{}class _ extends f{}class E extends f{}class S extends f{}class k extends f{}class x extends h{constructor(){super("Could not parse response content as the length limit was reached")}}class A extends h{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class C extends Error{constructor(e){super(e)}}let O=/^[a-z][a-z0-9+.-]*:/i,P=e=>O.test(e),R=e=>(R=Array.isArray)(e),I=R;function T(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let $=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new h(`${e} must be an integer`);if(t<0)throw new h(`${e} must be a positive integer`);return t},j=e=>{try{return JSON.parse(e)}catch(e){return}},N=e=>new Promise(t=>setTimeout(t,e)),M="5.9.0",L=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,F=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":M,"X-Stainless-OS":W(Deno.build.os),"X-Stainless-Arch":D(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":M,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":M,"X-Stainless-OS":W(globalThis.process.platform??"unknown"),"X-Stainless-Arch":D(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let n=t.exec(navigator.userAgent);if(n){let t=n[1]||0,r=n[2]||0,s=n[3]||0;return{browser:e,version:`${t}.${r}.${s}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":M,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":M,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},D=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",W=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",B=()=>r??(r=F());function U(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function q(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return U({start(){},async pull(e){let{done:n,value:r}=await t.next();n?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function H(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function X(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),n=t.cancel();t.releaseLock(),await n}let V=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),J="RFC3986",K=e=>String(e),Y={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:K},z=(e,t)=>(z=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(e,t),G=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function Q(e,t){if(R(e)){let n=[];for(let r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)}let Z={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},ee=function(e,t){Array.prototype.push.apply(e,R(t)?t:[t])},et={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,n,r,s)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===n)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let o="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,n=[];for(let e=0;e<t.length;++e){let r=t.charCodeAt(e);if(45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||"RFC1738"===s&&(40===r||41===r)){n[n.length]=t.charAt(e);continue}if(r<128){n[n.length]=G[r];continue}if(r<2048){n[n.length]=G[192|r>>6]+G[128|63&r];continue}if(r<55296||r>=57344){n[n.length]=G[224|r>>12]+G[128|r>>6&63]+G[128|63&r];continue}e+=1,r=65536+((1023&r)<<10|1023&t.charCodeAt(e)),n[n.length]=G[240|r>>18]+G[128|r>>12&63]+G[128|r>>6&63]+G[128|63&r]}o+=n.join("")}return o},encodeValuesOnly:!1,format:J,formatter:K,indices:!1,serializeDate:e=>(s??(s=Function.prototype.call.bind(Date.prototype.toISOString)))(e),skipNulls:!1,strictNullHandling:!1},en={};function er(e){let t;return(i??(i=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function es(e){let t;return(o??(o=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class ei{constructor(){tp.set(this,void 0),tm.set(this,void 0),a(this,tp,new Uint8Array,"f"),a(this,tm,null,"f")}decode(e){let t;if(null==e)return[];let n=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?er(e):e;a(this,tp,function(e){let t=0;for(let n of e)t+=n.length;let n=new Uint8Array(t),r=0;for(let t of e)n.set(t,r),r+=t.length;return n}([l(this,tp,"f"),n]),"f");let r=[];for(;null!=(t=function(e,t){for(let n=t??0;n<e.length;n++){if(10===e[n])return{preceding:n,index:n+1,carriage:!1};if(13===e[n])return{preceding:n,index:n+1,carriage:!0}}return null}(l(this,tp,"f"),l(this,tm,"f")));){if(t.carriage&&null==l(this,tm,"f")){a(this,tm,t.index,"f");continue}if(null!=l(this,tm,"f")&&(t.index!==l(this,tm,"f")+1||t.carriage)){r.push(es(l(this,tp,"f").subarray(0,l(this,tm,"f")-1))),a(this,tp,l(this,tp,"f").subarray(l(this,tm,"f")),"f"),a(this,tm,null,"f");continue}let e=null!==l(this,tm,"f")?t.preceding-1:t.preceding,n=es(l(this,tp,"f").subarray(0,e));r.push(n),a(this,tp,l(this,tp,"f").subarray(t.index),"f"),a(this,tm,null,"f")}return r}flush(){return l(this,tp,"f").length?this.decode("\n"):[]}}tp=new WeakMap,tm=new WeakMap,ei.NEWLINE_CHARS=new Set(["\n","\r"]),ei.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let eo={off:0,error:200,warn:300,info:400,debug:500},ea=(e,t,n)=>{if(e){if(Object.prototype.hasOwnProperty.call(eo,e))return e;eh(n).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eo))}`)}};function el(){}function eu(e,t,n){return!t||eo[e]>eo[n]?el:t[e].bind(t)}let ec={error:el,warn:el,info:el,debug:el},ed=new WeakMap;function eh(e){let t=e.logger,n=e.logLevel??"off";if(!t)return ec;let r=ed.get(t);if(r&&r[0]===n)return r[1];let s={error:eu("error",t,n),warn:eu("warn",t,n),info:eu("info",t,n),debug:eu("debug",t,n)};return ed.set(t,[n,s]),s}let ef=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);class ep{constructor(e,t,n){this.iterator=e,tg.set(this,void 0),this.controller=t,a(this,tg,n,"f")}static fromSSEResponse(e,t,n){let r=!1,s=n?eh(n):console;async function*i(){if(r)throw new h("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let n=!1;try{for await(let r of em(e,t))if(!n){if(r.data.startsWith("[DONE]")){n=!0;continue}if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw s.error("Could not parse message into JSON:",r.data),s.error("From chunk:",r.raw),e}if(t&&t.error)throw new f(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new f(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}}n=!0}catch(e){if(c(e))return;throw e}finally{n||t.abort()}}return new ep(i,t,n)}static fromReadableStream(e,t,n){let r=!1;async function*s(){let t=new ei;for await(let n of H(e))for(let e of t.decode(n))yield e;for(let e of t.flush())yield e}return new ep(async function*(){if(r)throw new h("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of s())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(c(e))return;throw e}finally{e||t.abort()}},t,n)}[(tg=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let e=[],t=[],n=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=n.next();e.push(r),t.push(r)}return r.shift()}});return[new ep(()=>r(e),this.controller,l(this,tg,"f")),new ep(()=>r(t),this.controller,l(this,tg,"f"))]}toReadableStream(){let e,t=this;return U({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:n,done:r}=await e.next();if(r)return t.close();let s=er(JSON.stringify(n)+"\n");t.enqueue(s)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*em(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new h("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new h("Attempted to iterate over a response with no body")}let n=new ey,r=new ei;for await(let t of eg(H(e.body)))for(let e of r.decode(t)){let t=n.decode(e);t&&(yield t)}for(let e of r.flush()){let t=n.decode(e);t&&(yield t)}}async function*eg(e){let t=new Uint8Array;for await(let n of e){let e;if(null==n)continue;let r=n instanceof ArrayBuffer?new Uint8Array(n):"string"==typeof n?er(n):n,s=new Uint8Array(t.length+r.length);for(s.set(t),s.set(r,t.length),t=s;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class ey{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,n,r]=function(e,t){let n=e.indexOf(":");return -1!==n?[e.substring(0,n),t,e.substring(n+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function ev(e,t){let{response:n,requestLogID:r,retryOfRequestLogID:s,startTime:i}=t,o=await (async()=>{if(t.options.stream)return(eh(e).debug("response",n.status,n.url,n.headers,n.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(n,t.controller,e):ep.fromSSEResponse(n,t.controller,e);if(204===n.status)return null;if(t.options.__binaryResponse)return n;let r=n.headers.get("content-type"),s=r?.split(";")[0]?.trim();return s?.includes("application/json")||s?.endsWith("+json")?ew(await n.json(),n):await n.text()})();return eh(e).debug(`[${r}] response parsed`,ef({retryOfRequestLogID:s,url:n.url,status:n.status,body:o,durationMs:Date.now()-i})),o}function ew(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class eb extends Promise{constructor(e,t,n=ev){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=n,ty.set(this,void 0),a(this,ty,e,"f")}_thenUnwrap(e){return new eb(l(this,ty,"f"),this.responsePromise,async(t,n)=>ew(e(await this.parseResponse(t,n),n),n.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(l(this,ty,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}ty=new WeakMap;class e_{constructor(e,t,n,r){tv.set(this,void 0),a(this,tv,e,"f"),this.options=r,this.response=t,this.body=n}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new h("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await l(this,tv,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(tv=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class eE extends eb{constructor(e,t,n){super(e,t,async(e,t)=>new n(e,t.response,await ev(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class eS extends e_{constructor(e,t,n,r){super(e,t,n,r),this.data=n.data||[],this.object=n.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class ek extends e_{constructor(e,t,n,r){super(e,t,n,r),this.data=n.data||[],this.has_more=n.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),n=t[t.length-1]?.id;return n?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:n}}:null}}let ex=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function eA(e,t,n){return ex(),new File(e,t??"unknown_file",n)}function eC(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let eO=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],eP=async(e,t)=>({...e,body:await eI(e.body,t)}),eR=new WeakMap,eI=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,n=eR.get(t);if(n)return n;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,n=new FormData;if(n.toString()===await new e(n).text())return!1;return!0}catch{return!0}})();return eR.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let n=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>eN(n,e,t))),n},eT=e=>e instanceof Blob&&"name"in e,e$=e=>"object"==typeof e&&null!==e&&(e instanceof Response||eO(e)||eT(e)),ej=e=>{if(e$(e))return!0;if(Array.isArray(e))return e.some(ej);if(e&&"object"==typeof e){for(let t in e)if(ej(e[t]))return!0}return!1},eN=async(e,t,n)=>{if(void 0!==n){if(null==n)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof n||"number"==typeof n||"boolean"==typeof n)e.append(t,String(n));else if(n instanceof Response)e.append(t,eA([await n.blob()],eC(n)));else if(eO(n))e.append(t,eA([await new Response(q(n)).blob()],eC(n)));else if(eT(n))e.append(t,n,eC(n));else if(Array.isArray(n))await Promise.all(n.map(n=>eN(e,t+"[]",n)));else if("object"==typeof n)await Promise.all(Object.entries(n).map(([n,r])=>eN(e,`${t}[${n}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${n} instead`)}},eM=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,eL=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&eM(e),eF=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function eD(e,t,n){if(ex(),eL(e=await e))return e instanceof File?e:eA([await e.arrayBuffer()],e.name);if(eF(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),eA(await eW(r),t,n)}let r=await eW(e);if(t||(t=eC(e)),!n?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(n={...n,type:e})}return eA(r,t,n)}async function eW(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(eM(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(eO(e))for await(let n of e)t.push(...await eW(n));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class eB{constructor(e){this._client=e}}function eU(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let eq=Object.freeze(Object.create(null)),eH=((e=eU)=>function(t,...n){let r;if(1===t.length)return t[0];let s=!1,i=[],o=t.reduce((t,r,o)=>{/[?#]/.test(r)&&(s=!0);let a=n[o],l=(s?encodeURIComponent:e)(""+a);return o!==n.length&&(null==a||"object"==typeof a&&a.toString===Object.getPrototypeOf(Object.getPrototypeOf(a.hasOwnProperty??eq)??eq)?.toString)&&(l=a+"",i.push({start:t.length+r.length,length:l.length,error:`Value of type ${Object.prototype.toString.call(a).slice(8,-1)} is not a valid path parameter`})),t+r+(o===n.length?"":l)},""),a=o.split(/[?#]/,1)[0],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(a));)i.push({start:r.index,length:r[0].length,error:`Value "${r[0]}" can't be safely passed as a path parameter`});if(i.sort((e,t)=>e.start-t.start),i.length>0){let e=0,t=i.reduce((t,n)=>{let r=" ".repeat(n.start-e),s="^".repeat(n.length);return e=n.start+n.length,t+r+s},"");throw new h(`Path parameters result in path with invalid segments:
${i.map(e=>e.error).join("\n")}
${o}
${t}`)}return o})(eU);class eX extends eB{list(e,t={},n){return this._client.getAPIList(eH`/chat/completions/${e}/messages`,ek,{query:t,...n})}}let eV=e=>e?.role==="assistant",eJ=e=>e?.role==="tool";class eK{constructor(){tw.add(this),this.controller=new AbortController,tb.set(this,void 0),t_.set(this,()=>{}),tE.set(this,()=>{}),tS.set(this,void 0),tk.set(this,()=>{}),tx.set(this,()=>{}),tA.set(this,{}),tC.set(this,!1),tO.set(this,!1),tP.set(this,!1),tR.set(this,!1),a(this,tb,new Promise((e,t)=>{a(this,t_,e,"f"),a(this,tE,t,"f")}),"f"),a(this,tS,new Promise((e,t)=>{a(this,tk,e,"f"),a(this,tx,t,"f")}),"f"),l(this,tb,"f").catch(()=>{}),l(this,tS,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},l(this,tw,"m",tI).bind(this))},0)}_connected(){this.ended||(l(this,t_,"f").call(this),this._emit("connect"))}get ended(){return l(this,tC,"f")}get errored(){return l(this,tO,"f")}get aborted(){return l(this,tP,"f")}abort(){this.controller.abort()}on(e,t){return(l(this,tA,"f")[e]||(l(this,tA,"f")[e]=[])).push({listener:t}),this}off(e,t){let n=l(this,tA,"f")[e];if(!n)return this;let r=n.findIndex(e=>e.listener===t);return r>=0&&n.splice(r,1),this}once(e,t){return(l(this,tA,"f")[e]||(l(this,tA,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,n)=>{a(this,tR,!0,"f"),"error"!==e&&this.once("error",n),this.once(e,t)})}async done(){a(this,tR,!0,"f"),await l(this,tS,"f")}_emit(e,...t){if(l(this,tC,"f"))return;"end"===e&&(a(this,tC,!0,"f"),l(this,tk,"f").call(this));let n=l(this,tA,"f")[e];if(n&&(l(this,tA,"f")[e]=n.filter(e=>!e.once),n.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];l(this,tR,"f")||n?.length||Promise.reject(e),l(this,tE,"f").call(this,e),l(this,tx,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];l(this,tR,"f")||n?.length||Promise.reject(e),l(this,tE,"f").call(this,e),l(this,tx,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function eY(e){return e?.$brand==="auto-parseable-response-format"}function ez(e){return e?.$brand==="auto-parseable-tool"}function eG(e,t){let n=e.choices.map(e=>{var n,r;if("length"===e.finish_reason)throw new x;if("content_filter"===e.finish_reason)throw new A;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let n=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:ez(n)?n.$parseRaw(t.function.arguments):n?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(n=t,r=e.message.content,n.response_format?.type!=="json_schema"?null:n.response_format?.type==="json_schema"?"$parseRaw"in n.response_format?n.response_format.$parseRaw(r):JSON.parse(r):null):null}}});return{...e,choices:n}}function eQ(e){return!!eY(e.response_format)||(e.tools?.some(e=>ez(e)||"function"===e.type&&!0===e.function.strict)??!1)}tb=new WeakMap,t_=new WeakMap,tE=new WeakMap,tS=new WeakMap,tk=new WeakMap,tx=new WeakMap,tA=new WeakMap,tC=new WeakMap,tO=new WeakMap,tP=new WeakMap,tR=new WeakMap,tw=new WeakSet,tI=function(e){if(a(this,tO,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new p),e instanceof p)return a(this,tP,!0,"f"),this._emit("abort",e);if(e instanceof h)return this._emit("error",e);if(e instanceof Error){let t=new h(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new h(String(e)))};class eZ extends eK{constructor(){super(...arguments),tT.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),eJ(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(eV(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new h("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),l(this,tT,"m",t$).call(this)}async finalMessage(){return await this.done(),l(this,tT,"m",tj).call(this)}async finalFunctionToolCall(){return await this.done(),l(this,tT,"m",tN).call(this)}async finalFunctionToolCallResult(){return await this.done(),l(this,tT,"m",tM).call(this)}async totalUsage(){return await this.done(),l(this,tT,"m",tL).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=l(this,tT,"m",tj).call(this);t&&this._emit("finalMessage",t);let n=l(this,tT,"m",t$).call(this);n&&this._emit("finalContent",n);let r=l(this,tT,"m",tN).call(this);r&&this._emit("finalFunctionToolCall",r);let s=l(this,tT,"m",tM).call(this);null!=s&&this._emit("finalFunctionToolCallResult",s),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",l(this,tT,"m",tL).call(this))}async _createChatCompletion(e,t,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),l(this,tT,"m",tF).call(this,t);let s=await e.chat.completions.create({...t,stream:!1},{...n,signal:this.controller.signal});return this._connected(),this._addChatCompletion(eG(s,t))}async _runChatCompletion(e,t,n){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,n)}async _runTools(e,t,n){let r="tool",{tool_choice:s="auto",stream:i,...o}=t,a="string"!=typeof s&&s?.function?.name,{maxChatCompletions:u=10}=n||{},c=t.tools.map(e=>{if(ez(e)){if(!e.$callback)throw new h("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),d={};for(let e of c)"function"===e.type&&(d[e.function.name||e.function.function.name]=e.function);let f="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<u;++t){let t=await this._createChatCompletion(e,{...o,tool_choice:s,tools:f,messages:[...this.messages]},n),i=t.choices[0]?.message;if(!i)throw new h("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let n=e.id,{name:s,arguments:i}=e.function,o=d[s];if(o){if(a&&a!==s){let e=`Invalid tool_call: ${JSON.stringify(s)}. ${JSON.stringify(a)} requested. Please try again`;this._addMessage({role:r,tool_call_id:n,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(s)}. Available options are: ${Object.keys(d).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:r,tool_call_id:n,content:e});continue}try{t="function"==typeof o.parse?await o.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:r,tool_call_id:n,content:e});continue}let u=await o.function(t,this),c=l(this,tT,"m",tD).call(this,u);if(this._addMessage({role:r,tool_call_id:n,content:c}),a)return}}}}tT=new WeakSet,t$=function(){return l(this,tT,"m",tj).call(this).content??null},tj=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(eV(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new h("stream ended without producing a ChatCompletionMessage with role=assistant")},tN=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(eV(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},tM=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(eJ(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},tL=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},tF=function(e){if(null!=e.n&&e.n>1)throw new h("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},tD=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class e0 extends eZ{static runTools(e,t,n){let r=new e0,s={...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,s)),r}_addMessage(e,t=!0){super._addMessage(e,t),eV(e)&&e.content&&this._emit("content",e.content)}}let e1={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class e2 extends Error{}class e4 extends Error{}let e3=(e,t)=>{let n=e.length,r=0,s=e=>{throw new e2(`${e} at position ${r}`)},i=e=>{throw new e4(`${e} at position ${r}`)},o=()=>(d(),r>=n&&s("Unexpected end of input"),'"'===e[r])?a():"{"===e[r]?l():"["===e[r]?u():"null"===e.substring(r,r+4)||e1.NULL&t&&n-r<4&&"null".startsWith(e.substring(r))?(r+=4,null):"true"===e.substring(r,r+4)||e1.BOOL&t&&n-r<4&&"true".startsWith(e.substring(r))?(r+=4,!0):"false"===e.substring(r,r+5)||e1.BOOL&t&&n-r<5&&"false".startsWith(e.substring(r))?(r+=5,!1):"Infinity"===e.substring(r,r+8)||e1.INFINITY&t&&n-r<8&&"Infinity".startsWith(e.substring(r))?(r+=8,1/0):"-Infinity"===e.substring(r,r+9)||e1.MINUS_INFINITY&t&&1<n-r&&n-r<9&&"-Infinity".startsWith(e.substring(r))?(r+=9,-1/0):"NaN"===e.substring(r,r+3)||e1.NAN&t&&n-r<3&&"NaN".startsWith(e.substring(r))?(r+=3,NaN):c(),a=()=>{let o=r,a=!1;for(r++;r<n&&('"'!==e[r]||a&&"\\"===e[r-1]);)a="\\"===e[r]&&!a,r++;if('"'==e.charAt(r))try{return JSON.parse(e.substring(o,++r-Number(a)))}catch(e){i(String(e))}else if(e1.STR&t)try{return JSON.parse(e.substring(o,r-Number(a))+'"')}catch(t){return JSON.parse(e.substring(o,e.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},l=()=>{r++,d();let i={};try{for(;"}"!==e[r];){if(d(),r>=n&&e1.OBJ&t)return i;let s=a();d(),r++;try{let e=o();Object.defineProperty(i,s,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(e1.OBJ&t)return i;throw e}d(),","===e[r]&&r++}}catch(e){if(e1.OBJ&t)return i;s("Expected '}' at end of object")}return r++,i},u=()=>{r++;let n=[];try{for(;"]"!==e[r];)n.push(o()),d(),","===e[r]&&r++}catch(e){if(e1.ARR&t)return n;s("Expected ']' at end of array")}return r++,n},c=()=>{if(0===r){"-"===e&&e1.NUM&t&&s("Not sure what '-' is");try{return JSON.parse(e)}catch(n){if(e1.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(n))}}let o=r;for("-"===e[r]&&r++;e[r]&&!",]}".includes(e[r]);)r++;r!=n||e1.NUM&t||s("Unterminated number literal");try{return JSON.parse(e.substring(o,r))}catch(n){"-"===e.substring(o,r)&&e1.NUM&t&&s("Not sure what '-' is");try{return JSON.parse(e.substring(o,e.lastIndexOf("e")))}catch(e){i(String(e))}}},d=()=>{for(;r<n&&" \n\r	".includes(e[r]);)r++};return o()},e5=e=>(function(e,t=e1.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return e3(e.trim(),t)})(e,e1.ALL^e1.NUM);class e9 extends eZ{constructor(e){super(),tW.add(this),tB.set(this,void 0),tU.set(this,void 0),tq.set(this,void 0),a(this,tB,e,"f"),a(this,tU,[],"f")}get currentChatCompletionSnapshot(){return l(this,tq,"f")}static fromReadableStream(e){let t=new e9(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,n){let r=new e9(t);return r._run(()=>r._runChatCompletion(e,{...t,stream:!0},{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createChatCompletion(e,t,n){super._createChatCompletion;let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),l(this,tW,"m",tH).call(this);let s=await e.chat.completions.create({...t,stream:!0},{...n,signal:this.controller.signal});for await(let e of(this._connected(),s))l(this,tW,"m",tV).call(this,e);if(s.controller.signal?.aborted)throw new p;return this._addChatCompletion(l(this,tW,"m",tY).call(this))}async _fromReadableStream(e,t){let n,r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),l(this,tW,"m",tH).call(this),this._connected();let s=ep.fromReadableStream(e,this.controller);for await(let e of s)n&&n!==e.id&&this._addChatCompletion(l(this,tW,"m",tY).call(this)),l(this,tW,"m",tV).call(this,e),n=e.id;if(s.controller.signal?.aborted)throw new p;return this._addChatCompletion(l(this,tW,"m",tY).call(this))}[(tB=new WeakMap,tU=new WeakMap,tq=new WeakMap,tW=new WeakSet,tH=function(){this.ended||a(this,tq,void 0,"f")},tX=function(e){let t=l(this,tU,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},l(this,tU,"f")[e.index]=t),t},tV=function(e){if(this.ended)return;let t=l(this,tW,"m",tG).call(this,e);for(let n of(this._emit("chunk",e,t),e.choices)){let e=t.choices[n.index];null!=n.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",n.delta.content,e.message.content),this._emit("content.delta",{delta:n.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=n.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:n.delta.refusal,snapshot:e.message.refusal}),n.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:n.logprobs?.content,snapshot:e.logprobs?.content??[]}),n.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:n.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let r=l(this,tW,"m",tX).call(this,e);for(let t of(e.finish_reason&&(l(this,tW,"m",tK).call(this,e),null!=r.current_tool_call_index&&l(this,tW,"m",tJ).call(this,e,r.current_tool_call_index)),n.delta.tool_calls??[]))r.current_tool_call_index!==t.index&&(l(this,tW,"m",tK).call(this,e),null!=r.current_tool_call_index&&l(this,tW,"m",tJ).call(this,e,r.current_tool_call_index)),r.current_tool_call_index=t.index;for(let t of n.delta.tool_calls??[]){let n=e.message.tool_calls?.[t.index];n?.type&&(n?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:n.function?.name,index:t.index,arguments:n.function.arguments,parsed_arguments:n.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):n?.type)}}},tJ=function(e,t){if(l(this,tW,"m",tX).call(this,e).done_tool_calls.has(t))return;let n=e.message.tool_calls?.[t];if(!n)throw Error("no tool call snapshot");if(!n.type)throw Error("tool call snapshot missing `type`");if("function"===n.type){let e=l(this,tB,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===n.function.name);this._emit("tool_calls.function.arguments.done",{name:n.function.name,index:t,arguments:n.function.arguments,parsed_arguments:ez(e)?e.$parseRaw(n.function.arguments):e?.function.strict?JSON.parse(n.function.arguments):null})}else n.type},tK=function(e){let t=l(this,tW,"m",tX).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let n=l(this,tW,"m",tz).call(this);this._emit("content.done",{content:e.message.content,parsed:n?n.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},tY=function(){if(this.ended)throw new h("stream has ended, this shouldn't happen");let e=l(this,tq,"f");if(!e)throw new h("request ended without sending any chunks");return a(this,tq,void 0,"f"),a(this,tU,[],"f"),function(e,t){var n;let{id:r,choices:s,created:i,model:o,system_fingerprint:a,...l}=e;return n={...l,id:r,choices:s.map(({message:t,finish_reason:n,index:r,logprobs:s,...i})=>{if(!n)throw new h(`missing finish_reason for choice ${r}`);let{content:o=null,function_call:a,tool_calls:l,...u}=t,c=t.role;if(!c)throw new h(`missing role for choice ${r}`);if(a){let{arguments:e,name:l}=a;if(null==e)throw new h(`missing function_call.arguments for choice ${r}`);if(!l)throw new h(`missing function_call.name for choice ${r}`);return{...i,message:{content:o,function_call:{arguments:e,name:l},role:c,refusal:t.refusal??null},finish_reason:n,index:r,logprobs:s}}return l?{...i,index:r,finish_reason:n,logprobs:s,message:{...u,role:c,content:o,refusal:t.refusal??null,tool_calls:l.map((t,n)=>{let{function:s,type:i,id:o,...a}=t,{arguments:l,name:u,...c}=s||{};if(null==o)throw new h(`missing choices[${r}].tool_calls[${n}].id
${e6(e)}`);if(null==i)throw new h(`missing choices[${r}].tool_calls[${n}].type
${e6(e)}`);if(null==u)throw new h(`missing choices[${r}].tool_calls[${n}].function.name
${e6(e)}`);if(null==l)throw new h(`missing choices[${r}].tool_calls[${n}].function.arguments
${e6(e)}`);return{...a,id:o,type:i,function:{...c,name:u,arguments:l}}})}}:{...i,message:{...u,content:o,role:c,refusal:t.refusal??null},finish_reason:n,index:r,logprobs:s}}),created:i,model:o,object:"chat.completion",...a?{system_fingerprint:a}:{}},t&&eQ(t)?eG(n,t):{...n,choices:n.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,l(this,tB,"f"))},tz=function(){let e=l(this,tB,"f")?.response_format;return eY(e)?e:null},tG=function(e){var t,n,r,s;let i=l(this,tq,"f"),{choices:o,...u}=e;for(let{delta:o,finish_reason:c,index:d,logprobs:h=null,...f}of(i?Object.assign(i,u):i=a(this,tq,{...u,choices:[]},"f"),e.choices)){let e=i.choices[d];if(e||(e=i.choices[d]={finish_reason:c,index:d,message:{},logprobs:h,...f}),h)if(e.logprobs){let{content:r,refusal:s,...i}=h;Object.assign(e.logprobs,i),r&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...r)),s&&((n=e.logprobs).refusal??(n.refusal=[]),e.logprobs.refusal.push(...s))}else e.logprobs=Object.assign({},h);if(c&&(e.finish_reason=c,l(this,tB,"f")&&eQ(l(this,tB,"f")))){if("length"===c)throw new x;if("content_filter"===c)throw new A}if(Object.assign(e,f),!o)continue;let{content:a,refusal:u,function_call:p,role:m,tool_calls:g,...y}=o;if(Object.assign(e.message,y),u&&(e.message.refusal=(e.message.refusal||"")+u),m&&(e.message.role=m),p&&(e.message.function_call?(p.name&&(e.message.function_call.name=p.name),p.arguments&&((r=e.message.function_call).arguments??(r.arguments=""),e.message.function_call.arguments+=p.arguments)):e.message.function_call=p),a&&(e.message.content=(e.message.content||"")+a,!e.message.refusal&&l(this,tW,"m",tz).call(this)&&(e.message.parsed=e5(e.message.content))),g)for(let{index:t,id:n,type:r,function:i,...o}of(e.message.tool_calls||(e.message.tool_calls=[]),g)){let a=(s=e.message.tool_calls)[t]??(s[t]={});Object.assign(a,o),n&&(a.id=n),r&&(a.type=r),i&&(a.function??(a.function={name:i.name??"",arguments:""})),i?.name&&(a.function.name=i.name),i?.arguments&&(a.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let n=e.tools?.find(e=>e.function?.name===t.function.name);return ez(n)||n?.function.strict||!1}(l(this,tB,"f"),a)&&(a.function.parsed_arguments=e5(a.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("chunk",n=>{let r=t.shift();r?r.resolve(n):e.push(n)}),this.on("end",()=>{for(let e of(n=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(n=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(n=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((e,n)=>t.push({resolve:e,reject:n})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new ep(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function e6(e){return JSON.stringify(e)}class e8 extends e9{static fromReadableStream(e){let t=new e8(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,n){let r=new e8(t),s={...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,s)),r}}class e7 extends eB{constructor(){super(...arguments),this.messages=new eX(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(eH`/chat/completions/${e}`,t)}update(e,t,n){return this._client.post(eH`/chat/completions/${e}`,{body:t,...n})}list(e={},t){return this._client.getAPIList("/chat/completions",ek,{query:e,...t})}delete(e,t){return this._client.delete(eH`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new h(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new h(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>eG(t,e))}runTools(e,t){return e.stream?e8.runTools(this._client,e,t):e0.runTools(this._client,e,t)}stream(e,t){return e9.createChatCompletion(this._client,e,t)}}e7.Messages=eX;class te extends eB{constructor(){super(...arguments),this.completions=new e7(this._client)}}te.Completions=e7;let tt=Symbol("brand.privateNullableHeaders"),tn=e=>{let t=new Headers,n=new Set;for(let r of e){let e=new Set;for(let[s,i]of function*(e){let t;if(!e)return;if(tt in e){let{values:t,nulls:n}=e;for(let e of(yield*t.entries(),n))yield[e,null];return}let n=!1;for(let r of(e instanceof Headers?t=e.entries():I(e)?t=e:(n=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=I(r[1])?r[1]:[r[1]],s=!1;for(let r of t)void 0!==r&&(n&&!s&&(s=!0,yield[e,null]),yield[e,r])}}(r)){let r=s.toLowerCase();e.has(r)||(t.delete(s),e.add(r)),null===i?(t.delete(s),n.add(r)):(t.append(s,i),n.delete(r))}}return{[tt]:!0,values:t,nulls:n}};class tr extends eB{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:tn([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class ts extends eB{create(e,t){return this._client.post("/audio/transcriptions",eP({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class ti extends eB{create(e,t){return this._client.post("/audio/translations",eP({body:e,...t,__metadata:{model:e.model}},this._client))}}class to extends eB{constructor(){super(...arguments),this.transcriptions=new ts(this._client),this.translations=new ti(this._client),this.speech=new tr(this._client)}}to.Transcriptions=ts,to.Translations=ti,to.Speech=tr;class ta extends eB{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(eH`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",ek,{query:e,...t})}cancel(e,t){return this._client.post(eH`/batches/${e}/cancel`,t)}}class tl extends eB{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eH`/assistants/${e}`,{...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,n){return this._client.post(eH`/assistants/${e}`,{body:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",ek,{query:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(eH`/assistants/${e}`,{...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class tu extends eB{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class tc extends eB{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class td extends eB{constructor(){super(...arguments),this.sessions=new tu(this._client),this.transcriptionSessions=new tc(this._client)}}td.Sessions=tu,td.TranscriptionSessions=tc;class th extends eB{create(e,t,n){return this._client.post(eH`/threads/${e}/messages`,{body:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}retrieve(e,t,n){let{thread_id:r}=t;return this._client.get(eH`/threads/${r}/messages/${e}`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}update(e,t,n){let{thread_id:r,...s}=t;return this._client.post(eH`/threads/${r}/messages/${e}`,{body:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}list(e,t={},n){return this._client.getAPIList(eH`/threads/${e}/messages`,ek,{query:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}delete(e,t,n){let{thread_id:r}=t;return this._client.delete(eH`/threads/${r}/messages/${e}`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}}class tf extends eB{retrieve(e,t,n){let{thread_id:r,run_id:s,...i}=t;return this._client.get(eH`/threads/${r}/runs/${s}/steps/${e}`,{query:i,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}list(e,t,n){let{thread_id:r,...s}=t;return this._client.getAPIList(eH`/threads/${r}/runs/${e}/steps`,ek,{query:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}}var tp,tm,tg,ty,tv,tw,tb,t_,tE,tS,tk,tx,tA,tC,tO,tP,tR,tI,tT,t$,tj,tN,tM,tL,tF,tD,tW,tB,tU,tq,tH,tX,tV,tJ,tK,tY,tz,tG,tQ,tZ,t0,t1,t2,t4,t3,t5,t9,t6,t8,t7,ne,nt,nn,nr,ns,ni,no,na,nl,nu,nc,nd,nh,nf,np,nm,ng,ny,nv,nw,nb,n_,nE,nS,nk,nx,nA=n(9641).Buffer;let nC=e=>{if(void 0!==nA){let t=nA.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),n=t.length,r=new Uint8Array(n);for(let e=0;e<n;e++)r[e]=t.charCodeAt(e);return Array.from(new Float32Array(r.buffer))}},nO=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class nP extends eK{constructor(){super(...arguments),tQ.add(this),t0.set(this,[]),t1.set(this,{}),t2.set(this,{}),t4.set(this,void 0),t3.set(this,void 0),t5.set(this,void 0),t9.set(this,void 0),t6.set(this,void 0),t8.set(this,void 0),t7.set(this,void 0),ne.set(this,void 0),nt.set(this,void 0)}[(t0=new WeakMap,t1=new WeakMap,t2=new WeakMap,t4=new WeakMap,t3=new WeakMap,t5=new WeakMap,t9=new WeakMap,t6=new WeakMap,t8=new WeakMap,t7=new WeakMap,ne=new WeakMap,nt=new WeakMap,tQ=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("event",n=>{let r=t.shift();r?r.resolve(n):e.push(n)}),this.on("end",()=>{for(let e of(n=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(n=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(n=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((e,n)=>t.push({resolve:e,reject:n})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new tZ;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),this._connected();let r=ep.fromReadableStream(e,this.controller);for await(let e of r)l(this,tQ,"m",nn).call(this,e);if(r.controller.signal?.aborted)throw new p;return this._addRun(l(this,tQ,"m",nr).call(this))}toReadableStream(){return new ep(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,n,r){let s=new tZ;return s._run(()=>s._runToolAssistantStream(e,t,n,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createToolAssistantStream(e,t,n,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));let i={...n,stream:!0},o=await e.submitToolOutputs(t,i,{...r,signal:this.controller.signal});for await(let e of(this._connected(),o))l(this,tQ,"m",nn).call(this,e);if(o.controller.signal?.aborted)throw new p;return this._addRun(l(this,tQ,"m",nr).call(this))}static createThreadAssistantStream(e,t,n){let r=new tZ;return r._run(()=>r._threadAssistantStream(e,t,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}static createAssistantStream(e,t,n,r){let s=new tZ;return s._run(()=>s._runAssistantStream(e,t,n,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}currentEvent(){return l(this,t7,"f")}currentRun(){return l(this,ne,"f")}currentMessageSnapshot(){return l(this,t4,"f")}currentRunStepSnapshot(){return l(this,nt,"f")}async finalRunSteps(){return await this.done(),Object.values(l(this,t1,"f"))}async finalMessages(){return await this.done(),Object.values(l(this,t2,"f"))}async finalRun(){if(await this.done(),!l(this,t3,"f"))throw Error("Final run was not received.");return l(this,t3,"f")}async _createThreadAssistantStream(e,t,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let s={...t,stream:!0},i=await e.createAndRun(s,{...n,signal:this.controller.signal});for await(let e of(this._connected(),i))l(this,tQ,"m",nn).call(this,e);if(i.controller.signal?.aborted)throw new p;return this._addRun(l(this,tQ,"m",nr).call(this))}async _createAssistantStream(e,t,n,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));let i={...n,stream:!0},o=await e.create(t,i,{...r,signal:this.controller.signal});for await(let e of(this._connected(),o))l(this,tQ,"m",nn).call(this,e);if(o.controller.signal?.aborted)throw new p;return this._addRun(l(this,tQ,"m",nr).call(this))}static accumulateDelta(e,t){for(let[n,r]of Object.entries(t)){if(!e.hasOwnProperty(n)){e[n]=r;continue}let t=e[n];if(null==t||"index"===n||"type"===n){e[n]=r;continue}if("string"==typeof t&&"string"==typeof r)t+=r;else if("number"==typeof t&&"number"==typeof r)t+=r;else if(T(t)&&T(r))t=this.accumulateDelta(t,r);else if(Array.isArray(t)&&Array.isArray(r)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...r);continue}for(let e of r){if(!T(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let n=e.index;if(null==n)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof n)throw Error(`Expected array delta entry \`index\` property to be a number but got ${n}`);let r=t[n];null==r?t.push(e):t[n]=this.accumulateDelta(r,e)}continue}else throw Error(`Unhandled record type: ${n}, deltaValue: ${r}, accValue: ${t}`);e[n]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,n){return await this._createThreadAssistantStream(t,e,n)}async _runAssistantStream(e,t,n,r){return await this._createAssistantStream(t,e,n,r)}async _runToolAssistantStream(e,t,n,r){return await this._createToolAssistantStream(t,e,n,r)}}tZ=nP,nn=function(e){if(!this.ended)switch(a(this,t7,e,"f"),l(this,tQ,"m",no).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":l(this,tQ,"m",nc).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":l(this,tQ,"m",ni).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":l(this,tQ,"m",ns).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},nr=function(){if(this.ended)throw new h("stream has ended, this shouldn't happen");if(!l(this,t3,"f"))throw Error("Final run has not been received");return l(this,t3,"f")},ns=function(e){let[t,n]=l(this,tQ,"m",nl).call(this,e,l(this,t4,"f"));for(let e of(a(this,t4,t,"f"),l(this,t2,"f")[t.id]=t,n)){let n=t.content[e.index];n?.type=="text"&&this._emit("textCreated",n.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let n of e.data.delta.content){if("text"==n.type&&n.text){let e=n.text,r=t.content[n.index];if(r&&"text"==r.type)this._emit("textDelta",e,r.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(n.index!=l(this,t5,"f")){if(l(this,t9,"f"))switch(l(this,t9,"f").type){case"text":this._emit("textDone",l(this,t9,"f").text,l(this,t4,"f"));break;case"image_file":this._emit("imageFileDone",l(this,t9,"f").image_file,l(this,t4,"f"))}a(this,t5,n.index,"f")}a(this,t9,t.content[n.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==l(this,t5,"f")){let t=e.data.content[l(this,t5,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,l(this,t4,"f"));break;case"text":this._emit("textDone",t.text,l(this,t4,"f"))}}l(this,t4,"f")&&this._emit("messageDone",e.data),a(this,t4,void 0,"f")}},ni=function(e){let t=l(this,tQ,"m",na).call(this,e);switch(a(this,nt,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let n=e.data.delta;if(n.step_details&&"tool_calls"==n.step_details.type&&n.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of n.step_details.tool_calls)e.index==l(this,t6,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(l(this,t8,"f")&&this._emit("toolCallDone",l(this,t8,"f")),a(this,t6,e.index,"f"),a(this,t8,t.step_details.tool_calls[e.index],"f"),l(this,t8,"f")&&this._emit("toolCallCreated",l(this,t8,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":a(this,nt,void 0,"f"),"tool_calls"==e.data.step_details.type&&l(this,t8,"f")&&(this._emit("toolCallDone",l(this,t8,"f")),a(this,t8,void 0,"f")),this._emit("runStepDone",e.data,t)}},no=function(e){l(this,t0,"f").push(e),this._emit("event",e)},na=function(e){switch(e.event){case"thread.run.step.created":return l(this,t1,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=l(this,t1,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let n=e.data;if(n.delta){let r=tZ.accumulateDelta(t,n.delta);l(this,t1,"f")[e.data.id]=r}return l(this,t1,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":l(this,t1,"f")[e.data.id]=e.data}if(l(this,t1,"f")[e.data.id])return l(this,t1,"f")[e.data.id];throw Error("No snapshot available")},nl=function(e,t){let n=[];switch(e.event){case"thread.message.created":return[e.data,n];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let r=e.data;if(r.delta.content)for(let e of r.delta.content)if(e.index in t.content){let n=t.content[e.index];t.content[e.index]=l(this,tQ,"m",nu).call(this,e,n)}else t.content[e.index]=e,n.push(e);return[t,n];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,n];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},nu=function(e,t){return tZ.accumulateDelta(t,e)},nc=function(e){switch(a(this,ne,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":a(this,t3,e.data,"f"),l(this,t8,"f")&&(this._emit("toolCallDone",l(this,t8,"f")),a(this,t8,void 0,"f"))}};class nR extends eB{constructor(){super(...arguments),this.steps=new tf(this._client)}create(e,t,n){let{include:r,...s}=t;return this._client.post(eH`/threads/${e}/runs`,{query:{include:r},body:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers]),stream:t.stream??!1})}retrieve(e,t,n){let{thread_id:r}=t;return this._client.get(eH`/threads/${r}/runs/${e}`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}update(e,t,n){let{thread_id:r,...s}=t;return this._client.post(eH`/threads/${r}/runs/${e}`,{body:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}list(e,t={},n){return this._client.getAPIList(eH`/threads/${e}/runs`,ek,{query:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}cancel(e,t,n){let{thread_id:r}=t;return this._client.post(eH`/threads/${r}/runs/${e}/cancel`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}async createAndPoll(e,t,n){let r=await this.create(e,t,n);return await this.poll(r.id,{thread_id:e},n)}createAndStream(e,t,n){return nP.createAssistantStream(e,this._client.beta.threads.runs,t,n)}async poll(e,t,n){let r=tn([n?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":n?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:s,response:i}=await this.retrieve(e,t,{...n,headers:{...n?.headers,...r}}).withResponse();switch(s.status){case"queued":case"in_progress":case"cancelling":let o=5e3;if(n?.pollIntervalMs)o=n.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(o=t)}}await N(o);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return s}}}stream(e,t,n){return nP.createAssistantStream(e,this._client.beta.threads.runs,t,n)}submitToolOutputs(e,t,n){let{thread_id:r,...s}=t;return this._client.post(eH`/threads/${r}/runs/${e}/submit_tool_outputs`,{body:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,n){let r=await this.submitToolOutputs(e,t,n);return await this.poll(r.id,t,n)}submitToolOutputsStream(e,t,n){return nP.createToolAssistantStream(e,this._client.beta.threads.runs,t,n)}}nR.Steps=tf;class nI extends eB{constructor(){super(...arguments),this.runs=new nR(this._client),this.messages=new th(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eH`/threads/${e}`,{...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,n){return this._client.post(eH`/threads/${e}`,{body:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}delete(e,t){return this._client.delete(eH`/threads/${e}`,{...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let n=await this.createAndRun(e,t);return await this.runs.poll(n.id,{thread_id:n.thread_id},t)}createAndRunStream(e,t){return nP.createThreadAssistantStream(e,this._client.beta.threads,t)}}nI.Runs=nR,nI.Messages=th;class nT extends eB{constructor(){super(...arguments),this.realtime=new td(this._client),this.assistants=new tl(this._client),this.threads=new nI(this._client)}}nT.Realtime=td,nT.Assistants=tl,nT.Threads=nI;class n$ extends eB{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class nj extends eB{retrieve(e,t,n){let{container_id:r}=t;return this._client.get(eH`/containers/${r}/files/${e}/content`,{...n,headers:tn([{Accept:"application/binary"},n?.headers]),__binaryResponse:!0})}}class nN extends eB{constructor(){super(...arguments),this.content=new nj(this._client)}create(e,t,n){return this._client.post(eH`/containers/${e}/files`,eP({body:t,...n},this._client))}retrieve(e,t,n){let{container_id:r}=t;return this._client.get(eH`/containers/${r}/files/${e}`,n)}list(e,t={},n){return this._client.getAPIList(eH`/containers/${e}/files`,ek,{query:t,...n})}delete(e,t,n){let{container_id:r}=t;return this._client.delete(eH`/containers/${r}/files/${e}`,{...n,headers:tn([{Accept:"*/*"},n?.headers])})}}nN.Content=nj;class nM extends eB{constructor(){super(...arguments),this.files=new nN(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(eH`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",ek,{query:e,...t})}delete(e,t){return this._client.delete(eH`/containers/${e}`,{...t,headers:tn([{Accept:"*/*"},t?.headers])})}}nM.Files=nN;class nL extends eB{create(e,t){let n=!!e.encoding_format,r=n?e.encoding_format:"base64";n&&eh(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let s=this._client.post("/embeddings",{body:{...e,encoding_format:r},...t});return n?s:(eh(this._client).debug("embeddings/decoding base64 embeddings from base64"),s._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=nC(t)}),e)))}}class nF extends eB{retrieve(e,t,n){let{eval_id:r,run_id:s}=t;return this._client.get(eH`/evals/${r}/runs/${s}/output_items/${e}`,n)}list(e,t,n){let{eval_id:r,...s}=t;return this._client.getAPIList(eH`/evals/${r}/runs/${e}/output_items`,ek,{query:s,...n})}}class nD extends eB{constructor(){super(...arguments),this.outputItems=new nF(this._client)}create(e,t,n){return this._client.post(eH`/evals/${e}/runs`,{body:t,...n})}retrieve(e,t,n){let{eval_id:r}=t;return this._client.get(eH`/evals/${r}/runs/${e}`,n)}list(e,t={},n){return this._client.getAPIList(eH`/evals/${e}/runs`,ek,{query:t,...n})}delete(e,t,n){let{eval_id:r}=t;return this._client.delete(eH`/evals/${r}/runs/${e}`,n)}cancel(e,t,n){let{eval_id:r}=t;return this._client.post(eH`/evals/${r}/runs/${e}`,n)}}nD.OutputItems=nF;class nW extends eB{constructor(){super(...arguments),this.runs=new nD(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(eH`/evals/${e}`,t)}update(e,t,n){return this._client.post(eH`/evals/${e}`,{body:t,...n})}list(e={},t){return this._client.getAPIList("/evals",ek,{query:e,...t})}delete(e,t){return this._client.delete(eH`/evals/${e}`,t)}}nW.Runs=nD;class nB extends eB{create(e,t){return this._client.post("/files",eP({body:e,...t},this._client))}retrieve(e,t){return this._client.get(eH`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",ek,{query:e,...t})}delete(e,t){return this._client.delete(eH`/files/${e}`,t)}content(e,t){return this._client.get(eH`/files/${e}/content`,{...t,headers:tn([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:n=18e5}={}){let r=new Set(["processed","error","deleted"]),s=Date.now(),i=await this.retrieve(e);for(;!i.status||!r.has(i.status);)if(await N(t),i=await this.retrieve(e),Date.now()-s>n)throw new g({message:`Giving up on waiting for file ${e} to finish processing after ${n} milliseconds.`});return i}}class nU extends eB{}class nq extends eB{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class nH extends eB{constructor(){super(...arguments),this.graders=new nq(this._client)}}nH.Graders=nq;class nX extends eB{create(e,t,n){return this._client.getAPIList(eH`/fine_tuning/checkpoints/${e}/permissions`,eS,{body:t,method:"post",...n})}retrieve(e,t={},n){return this._client.get(eH`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...n})}delete(e,t,n){let{fine_tuned_model_checkpoint:r}=t;return this._client.delete(eH`/fine_tuning/checkpoints/${r}/permissions/${e}`,n)}}class nV extends eB{constructor(){super(...arguments),this.permissions=new nX(this._client)}}nV.Permissions=nX;class nJ extends eB{list(e,t={},n){return this._client.getAPIList(eH`/fine_tuning/jobs/${e}/checkpoints`,ek,{query:t,...n})}}class nK extends eB{constructor(){super(...arguments),this.checkpoints=new nJ(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(eH`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",ek,{query:e,...t})}cancel(e,t){return this._client.post(eH`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},n){return this._client.getAPIList(eH`/fine_tuning/jobs/${e}/events`,ek,{query:t,...n})}pause(e,t){return this._client.post(eH`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(eH`/fine_tuning/jobs/${e}/resume`,t)}}nK.Checkpoints=nJ;class nY extends eB{constructor(){super(...arguments),this.methods=new nU(this._client),this.jobs=new nK(this._client),this.checkpoints=new nV(this._client),this.alpha=new nH(this._client)}}nY.Methods=nU,nY.Jobs=nK,nY.Checkpoints=nV,nY.Alpha=nH;class nz extends eB{}class nG extends eB{constructor(){super(...arguments),this.graderModels=new nz(this._client)}}nG.GraderModels=nz;class nQ extends eB{createVariation(e,t){return this._client.post("/images/variations",eP({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",eP({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class nZ extends eB{retrieve(e,t){return this._client.get(eH`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",eS,e)}delete(e,t){return this._client.delete(eH`/models/${e}`,t)}}class n0 extends eB{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function n1(e,t){let n=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let n=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(n)?n.$parseRaw(t.arguments):n?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let n=e.content.map(e=>{var n,r;return"output_text"===e.type?{...e,parsed:(n=t,r=e.text,n.text?.format?.type!=="json_schema"?null:"$parseRaw"in n.text?.format?(n.text?.format).$parseRaw(r):JSON.parse(r))}:e});return{...e,content:n}}return e}),r=Object.assign({},e,{output:n});return Object.getOwnPropertyDescriptor(e,"output_text")||n2(r),Object.defineProperty(r,"output_parsed",{enumerable:!0,get(){for(let e of r.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),r}function n2(e){let t=[];for(let n of e.output)if("message"===n.type)for(let e of n.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class n4 extends eK{constructor(e){super(),nd.add(this),nh.set(this,void 0),nf.set(this,void 0),np.set(this,void 0),a(this,nh,e,"f")}static createResponse(e,t,n){let r=new n4(t);return r._run(()=>r._createOrRetrieveResponse(e,t,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createOrRetrieveResponse(e,t,n){let r,s=n?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),l(this,nd,"m",nm).call(this);let i=null;for await(let s of("response_id"in t?(r=await e.responses.retrieve(t.response_id,{stream:!0},{...n,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):r=await e.responses.create({...t,stream:!0},{...n,signal:this.controller.signal}),this._connected(),r))l(this,nd,"m",ng).call(this,s,i);if(r.controller.signal?.aborted)throw new p;return l(this,nd,"m",ny).call(this)}[(nh=new WeakMap,nf=new WeakMap,np=new WeakMap,nd=new WeakSet,nm=function(){this.ended||a(this,nf,void 0,"f")},ng=function(e,t){if(this.ended)return;let n=(e,n)=>{(null==t||n.sequence_number>t)&&this._emit(e,n)},r=l(this,nd,"m",nv).call(this,e);switch(n("event",e),e.type){case"response.output_text.delta":{let t=r.output[e.output_index];if(!t)throw new h(`missing output at index ${e.output_index}`);if("message"===t.type){let r=t.content[e.content_index];if(!r)throw new h(`missing content at index ${e.content_index}`);if("output_text"!==r.type)throw new h(`expected content to be 'output_text', got ${r.type}`);n("response.output_text.delta",{...e,snapshot:r.text})}break}case"response.function_call_arguments.delta":{let t=r.output[e.output_index];if(!t)throw new h(`missing output at index ${e.output_index}`);"function_call"===t.type&&n("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:n(e.type,e)}},ny=function(){if(this.ended)throw new h("stream has ended, this shouldn't happen");let e=l(this,nf,"f");if(!e)throw new h("request ended without sending any events");a(this,nf,void 0,"f");let t=function(e,t){var n;return t&&(n=t,eY(n.text?.format))?n1(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,l(this,nh,"f"));return a(this,np,t,"f"),t},nv=function(e){let t=l(this,nf,"f");if(!t){if("response.created"!==e.type)throw new h(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return a(this,nf,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let n=t.output[e.output_index];if(!n)throw new h(`missing output at index ${e.output_index}`);"message"===n.type&&n.content.push(e.part);break}case"response.output_text.delta":{let n=t.output[e.output_index];if(!n)throw new h(`missing output at index ${e.output_index}`);if("message"===n.type){let t=n.content[e.content_index];if(!t)throw new h(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new h(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let n=t.output[e.output_index];if(!n)throw new h(`missing output at index ${e.output_index}`);"function_call"===n.type&&(n.arguments+=e.delta);break}case"response.completed":a(this,nf,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("event",n=>{let r=t.shift();r?r.resolve(n):e.push(n)}),this.on("end",()=>{for(let e of(n=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(n=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(n=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((e,n)=>t.push({resolve:e,reject:n})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=l(this,np,"f");if(!e)throw new h("stream ended without producing a ChatCompletion");return e}}class n3 extends eB{list(e,t={},n){return this._client.getAPIList(eH`/responses/${e}/input_items`,ek,{query:t,...n})}}class n5 extends eB{constructor(){super(...arguments),this.inputItems=new n3(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&n2(e),e))}retrieve(e,t={},n){return this._client.get(eH`/responses/${e}`,{query:t,...n,stream:t?.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&n2(e),e))}delete(e,t){return this._client.delete(eH`/responses/${e}`,{...t,headers:tn([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>n1(t,e))}stream(e,t){return n4.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(eH`/responses/${e}/cancel`,t)}}n5.InputItems=n3;class n9 extends eB{create(e,t,n){return this._client.post(eH`/uploads/${e}/parts`,eP({body:t,...n},this._client))}}class n6 extends eB{constructor(){super(...arguments),this.parts=new n9(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(eH`/uploads/${e}/cancel`,t)}complete(e,t,n){return this._client.post(eH`/uploads/${e}/complete`,{body:t,...n})}}n6.Parts=n9;let n8=async e=>{let t=await Promise.allSettled(e),n=t.filter(e=>"rejected"===e.status);if(n.length){for(let e of n)console.error(e.reason);throw Error(`${n.length} promise(s) failed - see the above errors`)}let r=[];for(let e of t)"fulfilled"===e.status&&r.push(e.value);return r};class n7 extends eB{create(e,t,n){return this._client.post(eH`/vector_stores/${e}/file_batches`,{body:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}retrieve(e,t,n){let{vector_store_id:r}=t;return this._client.get(eH`/vector_stores/${r}/file_batches/${e}`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}cancel(e,t,n){let{vector_store_id:r}=t;return this._client.post(eH`/vector_stores/${r}/file_batches/${e}/cancel`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}async createAndPoll(e,t,n){let r=await this.create(e,t);return await this.poll(e,r.id,n)}listFiles(e,t,n){let{vector_store_id:r,...s}=t;return this._client.getAPIList(eH`/vector_stores/${r}/file_batches/${e}/files`,ek,{query:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}async poll(e,t,n){let r=tn([n?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":n?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:s,response:i}=await this.retrieve(t,{vector_store_id:e},{...n,headers:r}).withResponse();switch(s.status){case"in_progress":let o=5e3;if(n?.pollIntervalMs)o=n.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(o=t)}}await N(o);break;case"failed":case"cancelled":case"completed":return s}}}async uploadAndPoll(e,{files:t,fileIds:n=[]},r){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let s=Math.min(r?.maxConcurrency??5,t.length),i=this._client,o=t.values(),a=[...n];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},r);a.push(e.id)}}let u=Array(s).fill(o).map(l);return await n8(u),await this.createAndPoll(e,{file_ids:a})}}class re extends eB{create(e,t,n){return this._client.post(eH`/vector_stores/${e}/files`,{body:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}retrieve(e,t,n){let{vector_store_id:r}=t;return this._client.get(eH`/vector_stores/${r}/files/${e}`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}update(e,t,n){let{vector_store_id:r,...s}=t;return this._client.post(eH`/vector_stores/${r}/files/${e}`,{body:s,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}list(e,t={},n){return this._client.getAPIList(eH`/vector_stores/${e}/files`,ek,{query:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}delete(e,t,n){let{vector_store_id:r}=t;return this._client.delete(eH`/vector_stores/${r}/files/${e}`,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}async createAndPoll(e,t,n){let r=await this.create(e,t,n);return await this.poll(e,r.id,n)}async poll(e,t,n){let r=tn([n?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":n?.pollIntervalMs?.toString()??void 0}]);for(;;){let s=await this.retrieve(t,{vector_store_id:e},{...n,headers:r}).withResponse(),i=s.data;switch(i.status){case"in_progress":let o=5e3;if(n?.pollIntervalMs)o=n.pollIntervalMs;else{let e=s.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(o=t)}}await N(o);break;case"failed":case"completed":return i}}}async upload(e,t,n){let r=await this._client.files.create({file:t,purpose:"assistants"},n);return this.create(e,{file_id:r.id},n)}async uploadAndPoll(e,t,n){let r=await this.upload(e,t,n);return await this.poll(e,r.id,n)}content(e,t,n){let{vector_store_id:r}=t;return this._client.getAPIList(eH`/vector_stores/${r}/files/${e}/content`,eS,{...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}}class rt extends eB{constructor(){super(...arguments),this.files=new re(this._client),this.fileBatches=new n7(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eH`/vector_stores/${e}`,{...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,n){return this._client.post(eH`/vector_stores/${e}`,{body:t,...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",ek,{query:e,...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(eH`/vector_stores/${e}`,{...t,headers:tn([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,n){return this._client.getAPIList(eH`/vector_stores/${e}/search`,eS,{body:t,method:"post",...n,headers:tn([{"OpenAI-Beta":"assistants=v2"},n?.headers])})}}rt.Files=re,rt.FileBatches=n7;var rn=n(9641).Buffer;class rr extends eB{async unwrap(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._client.webhookSecret,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:300;return await this.verifySignature(e,t,n,r),JSON.parse(e)}async verifySignature(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._client.webhookSecret,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:300;if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");l(this,nw,"m",nb).call(this,n);let s=tn([t]).values,i=l(this,nw,"m",n_).call(this,s,"webhook-signature"),o=l(this,nw,"m",n_).call(this,s,"webhook-timestamp"),a=l(this,nw,"m",n_).call(this,s,"webhook-id"),u=parseInt(o,10);if(isNaN(u))throw new C("Invalid webhook timestamp format");let c=Math.floor(Date.now()/1e3);if(c-u>r)throw new C("Webhook timestamp is too old");if(u>c+r)throw new C("Webhook timestamp is too new");let d=i.split(" ").map(e=>e.startsWith("v1,")?e.substring(3):e),h=n.startsWith("whsec_")?rn.from(n.replace("whsec_",""),"base64"):rn.from(n,"utf-8"),f=a?"".concat(a,".").concat(o,".").concat(e):"".concat(o,".").concat(e),p=await crypto.subtle.importKey("raw",h,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let e of d)try{let t=rn.from(e,"base64");if(await crypto.subtle.verify("HMAC",p,t,new TextEncoder().encode(f)))return}catch(e){continue}throw new C("The given webhook signature does not match the expected signature")}constructor(){super(...arguments),nw.add(this)}}nw=new WeakSet,nb=function(e){if("string"!=typeof e||0===e.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},n_=function(e,t){if(!e)throw Error("Headers are required");let n=e.get(t);if(null==n)throw Error("Missing required header: ".concat(t));return n};class rs{constructor({baseURL:e=nO("OPENAI_BASE_URL"),apiKey:t=nO("OPENAI_API_KEY"),organization:n=nO("OPENAI_ORG_ID")??null,project:r=nO("OPENAI_PROJECT_ID")??null,webhookSecret:s=nO("OPENAI_WEBHOOK_SECRET")??null,...i}={}){if(nE.add(this),nk.set(this,void 0),this.completions=new n$(this),this.chat=new te(this),this.embeddings=new nL(this),this.files=new nB(this),this.images=new nQ(this),this.audio=new to(this),this.moderations=new n0(this),this.models=new nZ(this),this.fineTuning=new nY(this),this.graders=new nG(this),this.vectorStores=new rt(this),this.webhooks=new rr(this),this.beta=new nT(this),this.batches=new ta(this),this.uploads=new n6(this),this.responses=new n5(this),this.evals=new nW(this),this.containers=new nM(this),void 0===t)throw new h("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let o={apiKey:t,organization:n,project:r,webhookSecret:s,...i,baseURL:e||"https://api.openai.com/v1"};if(!o.dangerouslyAllowBrowser&&L())throw new h("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=o.baseURL,this.timeout=o.timeout??nS.DEFAULT_TIMEOUT,this.logger=o.logger??console;let l="warn";this.logLevel=l,this.logLevel=ea(o.logLevel,"ClientOptions.logLevel",this)??ea(nO("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??l,this.fetchOptions=o.fetchOptions,this.maxRetries=o.maxRetries??2,this.fetch=o.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),a(this,nk,V,"f"),this._options=o,this.apiKey=t,this.organization=n,this.project=r,this.webhookSecret=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}async authHeaders(e){return tn([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let n,r,s=e,i=function(e=et){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let n=e.charset||et.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=J;if(void 0!==e.format){if(!z(Y,e.format))throw TypeError("Unknown format option provided.");r=e.format}let s=Y[r],i=et.filter;if(("function"==typeof e.filter||R(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in Z?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":et.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let o=void 0===e.allowDots?!0==!!e.encodeDotInKeys||et.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:et.addQueryPrefix,allowDots:o,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:et.allowEmptyArrays,arrayFormat:t,charset:n,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:et.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?et.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:et.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:et.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:et.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:et.encodeValuesOnly,filter:i,format:r,formatter:s,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:et.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:et.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:et.strictNullHandling}}(t);"function"==typeof i.filter?s=(0,i.filter)("",s):R(i.filter)&&(n=i.filter);let o=[];if("object"!=typeof s||null===s)return"";let a=Z[i.arrayFormat],l="comma"===a&&i.commaRoundTrip;n||(n=Object.keys(s)),i.sort&&n.sort(i.sort);let u=new WeakMap;for(let e=0;e<n.length;++e){let t=n[e];i.skipNulls&&null===s[t]||ee(o,function e(t,n,r,s,i,o,a,l,u,c,d,h,f,p,m,g,y,v){var w,b;let _,E=t,S=v,k=0,x=!1;for(;void 0!==(S=S.get(en))&&!x;){let e=S.get(t);if(k+=1,void 0!==e)if(e===k)throw RangeError("Cyclic object value");else x=!0;void 0===S.get(en)&&(k=0)}if("function"==typeof c?E=c(n,E):E instanceof Date?E=f?.(E):"comma"===r&&R(E)&&(E=Q(E,function(e){return e instanceof Date?f?.(e):e})),null===E){if(o)return u&&!g?u(n,et.encoder,y,"key",p):n;E=""}if("string"==typeof(w=E)||"number"==typeof w||"boolean"==typeof w||"symbol"==typeof w||"bigint"==typeof w||(b=E)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(u){let e=g?n:u(n,et.encoder,y,"key",p);return[m?.(e)+"="+m?.(u(E,et.encoder,y,"value",p))]}return[m?.(n)+"="+m?.(String(E))]}let A=[];if(void 0===E)return A;if("comma"===r&&R(E))g&&u&&(E=Q(E,u)),_=[{value:E.length>0?E.join(",")||null:void 0}];else if(R(c))_=c;else{let e=Object.keys(E);_=d?e.sort(d):e}let C=l?String(n).replace(/\./g,"%2E"):String(n),O=s&&R(E)&&1===E.length?C+"[]":C;if(i&&R(E)&&0===E.length)return O+"[]";for(let n=0;n<_.length;++n){let w=_[n],b="object"==typeof w&&void 0!==w.value?w.value:E[w];if(a&&null===b)continue;let S=h&&l?w.replace(/\./g,"%2E"):w,x=R(E)?"function"==typeof r?r(O,S):O:O+(h?"."+S:"["+S+"]");v.set(t,k);let C=new WeakMap;C.set(en,v),ee(A,e(b,x,r,s,i,o,a,l,"comma"===r&&g&&R(E)?null:u,c,d,h,f,p,m,g,y,C))}return A}(s[t],t,a,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,u))}let c=o.join(i.delimiter),d=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),c.length>0?d+c:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${M}`}defaultIdempotencyKey(){return`stainless-node-retry-${u()}`}makeStatusError(e,t,n,r){return f.generate(e,t,n,r)}buildURL(e,t,n){let r=!l(this,nE,"m",nx).call(this)&&n||this.baseURL,s=new URL(P(e)?e:r+(r.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),i=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(i)&&(t={...i,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:n}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,n){return this.request(Promise.resolve(n).then(n=>({method:e,path:t,...n})))}request(e,t=null){return new eb(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,n){let r=await e,s=r.maxRetries??this.maxRetries;null==t&&(t=s),await this.prepareOptions(r);let{req:i,url:o,timeout:a}=await this.buildRequest(r,{retryCount:s-t});await this.prepareRequest(i,{url:o,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),u=void 0===n?"":`, retryOf: ${n}`,h=Date.now();if(eh(this).debug(`[${l}] sending request`,ef({retryOfRequestLogID:n,method:r.method,url:o,options:r,headers:i.headers})),r.signal?.aborted)throw new p;let f=new AbortController,y=await this.fetchWithTimeout(o,i,a,f).catch(d),v=Date.now();if(y instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new p;let s=c(y)||/timed? ?out/i.test(String(y)+("cause"in y?String(y.cause):""));if(t)return eh(this).info(`[${l}] connection ${s?"timed out":"failed"} - ${e}`),eh(this).debug(`[${l}] connection ${s?"timed out":"failed"} (${e})`,ef({retryOfRequestLogID:n,url:o,durationMs:v-h,message:y.message})),this.retryRequest(r,t,n??l);if(eh(this).info(`[${l}] connection ${s?"timed out":"failed"} - error; no more retries left`),eh(this).debug(`[${l}] connection ${s?"timed out":"failed"} (error; no more retries left)`,ef({retryOfRequestLogID:n,url:o,durationMs:v-h,message:y.message})),s)throw new g;throw new m({cause:y})}let w=[...y.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),b=`[${l}${u}${w}] ${i.method} ${o} ${y.ok?"succeeded":"failed"} with status ${y.status} in ${v-h}ms`;if(!y.ok){let e=await this.shouldRetry(y);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await X(y.body),eh(this).info(`${b} - ${e}`),eh(this).debug(`[${l}] response error (${e})`,ef({retryOfRequestLogID:n,url:y.url,status:y.status,headers:y.headers,durationMs:v-h})),this.retryRequest(r,t,n??l,y.headers)}let s=e?"error; no more retries left":"error; not retryable";eh(this).info(`${b} - ${s}`);let i=await y.text().catch(e=>d(e).message),o=j(i),a=o?void 0:i;throw eh(this).debug(`[${l}] response error (${s})`,ef({retryOfRequestLogID:n,url:y.url,status:y.status,headers:y.headers,message:a,durationMs:Date.now()-h})),this.makeStatusError(y.status,o,a,y.headers)}return eh(this).info(b),eh(this).debug(`[${l}] response start`,ef({retryOfRequestLogID:n,url:y.url,status:y.status,headers:y.headers,durationMs:v-h})),{response:y,options:r,controller:f,requestLogID:l,retryOfRequestLogID:n,startTime:h}}getAPIList(e,t,n){return this.requestAPIList(t,{method:"get",path:e,...n})}requestAPIList(e,t){return new eE(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,n,r){let{signal:s,method:i,...o}=t||{};s&&s.addEventListener("abort",()=>r.abort());let a=setTimeout(()=>r.abort(),n),l=globalThis.ReadableStream&&o.body instanceof globalThis.ReadableStream||"object"==typeof o.body&&null!==o.body&&Symbol.asyncIterator in o.body,u={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...o};i&&(u.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,u)}finally{clearTimeout(a)}}async shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,n,r){let s,i=r?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(s=e)}let o=r?.get("retry-after");if(o&&!s){let e=parseFloat(o);s=Number.isNaN(e)?Date.parse(o)-Date.now():1e3*e}if(!(s&&0<=s&&s<6e4)){let n=e.maxRetries??this.maxRetries;s=this.calculateDefaultRetryTimeoutMillis(t,n)}return await N(s),this.makeRequest(e,t-1,n)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}async buildRequest(e,{retryCount:t=0}={}){let n={...e},{method:r,path:s,query:i,defaultBaseURL:o}=n,a=this.buildURL(s,i,o);"timeout"in n&&$("timeout",n.timeout),n.timeout=n.timeout??this.timeout;let{bodyHeaders:l,body:u}=this.buildBody({options:n}),c=await this.buildHeaders({options:e,method:r,bodyHeaders:l,retryCount:t});return{req:{method:r,headers:c,...n.signal&&{signal:n.signal},...globalThis.ReadableStream&&u instanceof globalThis.ReadableStream&&{duplex:"half"},...u&&{body:u},...this.fetchOptions??{},...n.fetchOptions??{}},url:a,timeout:n.timeout}}async buildHeaders({options:e,method:t,bodyHeaders:n,retryCount:r}){let s={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),s[this.idempotencyHeader]=e.idempotencyKey);let i=tn([s,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...B(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,n,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let n=tn([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&n.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:q(e)}:l(this,nk,"f").call(this,{body:e,headers:n})}}nS=rs,nk=new WeakMap,nE=new WeakSet,nx=function(){return"https://api.openai.com/v1"!==this.baseURL},rs.OpenAI=nS,rs.DEFAULT_TIMEOUT=6e5,rs.OpenAIError=h,rs.APIError=f,rs.APIConnectionError=m,rs.APIConnectionTimeoutError=g,rs.APIUserAbortError=p,rs.NotFoundError=b,rs.ConflictError=_,rs.RateLimitError=S,rs.BadRequestError=y,rs.AuthenticationError=v,rs.InternalServerError=k,rs.PermissionDeniedError=w,rs.UnprocessableEntityError=E,rs.InvalidWebhookSignatureError=C,rs.toFile=eD,rs.Completions=n$,rs.Chat=te,rs.Embeddings=nL,rs.Files=nB,rs.Images=nQ,rs.Audio=to,rs.Moderations=n0,rs.Models=nZ,rs.FineTuning=nY,rs.Graders=nG,rs.VectorStores=rt,rs.Webhooks=rr,rs.Beta=nT,rs.Batches=ta,rs.Uploads=n6,rs.Responses=n5,rs.Evals=nW,rs.Containers=nM,n(9509)},9676:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},9869:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),o=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:h,...f}=e;return(0,r.createElement)("svg",{ref:t,...u,width:s,height:s,stroke:n,strokeWidth:o?24*Number(i)/Number(s):i,className:a("lucide",c),...!d&&!l(f)&&{"aria-hidden":"true"},...f},[...h.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:l,...u}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:a("lucide-".concat(s(o(e))),"lucide-".concat(e),l),...u})});return n.displayName=o(e),n}}}]);