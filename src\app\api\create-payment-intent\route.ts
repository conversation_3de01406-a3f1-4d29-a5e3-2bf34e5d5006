import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { createPaymentIntent, CREDIT_PACKAGES } from '@/lib/stripe'
import type { CreditPackage } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { packageType } = await request.json()
    
    if (!packageType || !CREDIT_PACKAGES[packageType as CreditPackage]) {
      return NextResponse.json({ error: 'Invalid package type' }, { status: 400 })
    }

    // Create payment intent
    const { clientSecret, paymentIntentId } = await createPaymentIntent(
      user.id,
      packageType as CreditPackage,
      user.email || undefined
    )

    // Store transaction record
    const package_ = CREDIT_PACKAGES[packageType as CreditPackage]
    await supabase
      .from('credit_transactions')
      .insert({
        user_id: user.id,
        amount: package_.price,
        credits_added: package_.credits,
        stripe_payment_intent_id: paymentIntentId,
        status: 'pending'
      })

    return NextResponse.json({
      clientSecret,
      paymentIntentId,
      package: package_
    })

  } catch (error) {
    console.error('Payment intent creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}
