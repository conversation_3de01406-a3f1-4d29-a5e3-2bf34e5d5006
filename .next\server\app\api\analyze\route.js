(()=>{var e={};e.id=786,e.ids=[786],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,s)=>{"use strict";function n(e){let t=e.headers.get("x-forwarded-for"),s=e.headers.get("x-real-ip"),n=e.headers.get("cf-connecting-ip");return n||s||(t?t.split(",")[0].trim():"127.0.0.1")}s.d(t,{Tf:()=>n})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2049:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>a,hS:()=>i,sy:()=>o});var n=s(3769),r=s(6621);async function i(e,t){let s=await (0,n.d)();if(e){let{data:t}=await s.from("user_profiles").select("*").eq("id",e).single();if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!1};let n=new Date().toISOString().split("T")[0],r=t.last_use_date,i=t.daily_uses;r!==n&&(i=0,await s.from("user_profiles").update({daily_uses:0,last_use_date:n}).eq("id",e));let a=Math.max(0,3-i)+t.credits,o=a>0;return{canUse:o,remainingUses:a,isAnonymous:!1,needsCredits:!o,canUseLevel:{low:a>=1,medium:a>=2,high:a>=3}}}{if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!0,needsCredits:!1};let e=new Date().toISOString().split("T")[0],s=(0,r.LE)(),{data:n}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e),i=1>(n?.length||0);return{canUse:i,remainingUses:+!!i,isAnonymous:!0,needsCredits:!1,canUseLevel:{low:i,medium:!1,high:!1}}}}async function a(e,t,s=1){let i=await (0,n.d)();if(e){let{data:t}=await i.from("user_profiles").select("*").eq("id",e).single();if(!t)return!1;let n=new Date().toISOString().split("T")[0],r=t.daily_uses;t.last_use_date!==n&&(r=0);let a=Math.max(0,3-r);if(a+t.credits<s)return!1;let o=s,l=r,c=t.credits;if(a>0){let e=Math.min(a,o);l+=e,o-=e}return o>0&&(c-=o),await i.from("user_profiles").update({daily_uses:l,credits:c,last_use_date:n,updated_at:new Date().toISOString()}).eq("id",e),!0}{if(!t)return!1;let e=new Date().toISOString().split("T")[0],s=(0,r.LE)(),{data:n}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e).single();return!n&&(await s.from("anonymous_uses").insert({ip_address:t,use_date:e}),!0)}}async function o(e,t){let s=await (0,n.d)(),{data:r}=await s.from("user_profiles").select("credits").eq("id",e).single();return!!r&&(await s.from("user_profiles").update({credits:r.credits+t,updated_at:new Date().toISOString()}).eq("id",e),!0)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var n=s(4386),r=s(4999);let i=async()=>{let e=await (0,r.UL)();return(0,n.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:t=>e.get(t)?.value,set(t,s,n){e.set({name:t,value:s,...n})},remove(t,s){e.set({name:t,value:"",...s})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6621:(e,t,s)=>{"use strict";s.d(t,{LE:()=>o});var n=s(6437);s(4386);let r=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",i=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",a=()=>{let e=r(),t=i();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,n.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})},o=()=>a()},7437:(e,t,s)=>{"use strict";let n,r,i,a;s.r(t),s.d(t,{patchFetch:()=>nb,routeModule:()=>ng,serverHooks:()=>n_,workAsyncStorage:()=>ny,workUnitAsyncStorage:()=>nw});var o,l,c,u,h,d,p,f,m,g,y,w,_,b,v,x,S,A,I,$,O,k,R,E,P,C,N,T,j,M,D,L,U,B,q,W,F,H,X,J,K,z,V,Y,Q,G,Z,ee,et,es,en,er,ei,ea,eo,el,ec,eu,eh,ed,ep,ef,em,eg,ey,ew,e_,eb,ev,ex,eS,eA,eI,e$,eO,ek,eR={};s.r(eR),s.d(eR,{POST:()=>nm});var eE=s(6559),eP=s(8088),eC=s(7719),eN=s(2190),eT=s(3769);function ej(e,t,s,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s}function eM(e,t,s,n){if("a"===s&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}let eD=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eD=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eL(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eU=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eB extends Error{}class eq extends eB{constructor(e,t,s,n){super(`${eq.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new eF({message:s,cause:eU(t)});let r=t?.error;return 400===e?new eX(e,r,s,n):401===e?new eJ(e,r,s,n):403===e?new eK(e,r,s,n):404===e?new ez(e,r,s,n):409===e?new eV(e,r,s,n):422===e?new eY(e,r,s,n):429===e?new eQ(e,r,s,n):e>=500?new eG(e,r,s,n):new eq(e,r,s,n)}}class eW extends eq{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eF extends eq{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eH extends eF{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eX extends eq{}class eJ extends eq{}class eK extends eq{}class ez extends eq{}class eV extends eq{}class eY extends eq{}class eQ extends eq{}class eG extends eq{}class eZ extends eB{constructor(){super("Could not parse response content as the length limit was reached")}}class e0 extends eB{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class e1 extends Error{constructor(e){super(e)}}let e2=/^[a-z][a-z0-9+.-]*:/i,e3=e=>e2.test(e),e4=e=>(e4=Array.isArray)(e),e6=e4;function e8(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let e5=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eB(`${e} must be an integer`);if(t<0)throw new eB(`${e} must be a positive integer`);return t},e9=e=>{try{return JSON.parse(e)}catch(e){return}},e7=e=>new Promise(t=>setTimeout(t,e)),te="5.9.0",tt=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,ts=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":tr(Deno.build.os),"X-Stainless-Arch":tn(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":tr(globalThis.process.platform??"unknown"),"X-Stainless-Arch":tn(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,n=s[2]||0,r=s[3]||0;return{browser:e,version:`${t}.${n}.${r}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},tn=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tr=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",ti=()=>n??(n=ts());function ta(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function to(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return ta({start(){},async pull(e){let{done:s,value:n}=await t.next();s?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function tl(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tc(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let tu=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),th="RFC3986",td=e=>String(e),tp={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:td},tf=(e,t)=>(tf=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(e,t),tm=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tg(e,t){if(e4(e)){let s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}let ty={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tw=function(e,t){Array.prototype.push.apply(e,e4(t)?t:[t])},t_={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,n,r)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===s)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,s=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===r&&(40===n||41===n)){s[s.length]=t.charAt(e);continue}if(n<128){s[s.length]=tm[n];continue}if(n<2048){s[s.length]=tm[192|n>>6]+tm[128|63&n];continue}if(n<55296||n>=57344){s[s.length]=tm[224|n>>12]+tm[128|n>>6&63]+tm[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),s[s.length]=tm[240|n>>18]+tm[128|n>>12&63]+tm[128|n>>6&63]+tm[128|63&n]}a+=s.join("")}return a},encodeValuesOnly:!1,format:th,formatter:td,indices:!1,serializeDate:e=>(r??(r=Function.prototype.call.bind(Date.prototype.toISOString)))(e),skipNulls:!1,strictNullHandling:!1},tb={};function tv(e){let t;return(i??(i=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tx(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class tS{constructor(){o.set(this,void 0),l.set(this,void 0),ej(this,o,new Uint8Array,"f"),ej(this,l,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tv(e):e;ej(this,o,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),n=0;for(let t of e)s.set(t,n),n+=t.length;return s}([eM(this,o,"f"),s]),"f");let n=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eM(this,o,"f"),eM(this,l,"f")));){if(t.carriage&&null==eM(this,l,"f")){ej(this,l,t.index,"f");continue}if(null!=eM(this,l,"f")&&(t.index!==eM(this,l,"f")+1||t.carriage)){n.push(tx(eM(this,o,"f").subarray(0,eM(this,l,"f")-1))),ej(this,o,eM(this,o,"f").subarray(eM(this,l,"f")),"f"),ej(this,l,null,"f");continue}let e=null!==eM(this,l,"f")?t.preceding-1:t.preceding,s=tx(eM(this,o,"f").subarray(0,e));n.push(s),ej(this,o,eM(this,o,"f").subarray(t.index),"f"),ej(this,l,null,"f")}return n}flush(){return eM(this,o,"f").length?this.decode("\n"):[]}}o=new WeakMap,l=new WeakMap,tS.NEWLINE_CHARS=new Set(["\n","\r"]),tS.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let tA={off:0,error:200,warn:300,info:400,debug:500},tI=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(tA,e))return e;tE(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(tA))}`)}};function t$(){}function tO(e,t,s){return!t||tA[e]>tA[s]?t$:t[e].bind(t)}let tk={error:t$,warn:t$,info:t$,debug:t$},tR=new WeakMap;function tE(e){let t=e.logger,s=e.logLevel??"off";if(!t)return tk;let n=tR.get(t);if(n&&n[0]===s)return n[1];let r={error:tO("error",t,s),warn:tO("warn",t,s),info:tO("info",t,s),debug:tO("debug",t,s)};return tR.set(t,[s,r]),r}let tP=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);class tC{constructor(e,t,s){this.iterator=e,c.set(this,void 0),this.controller=t,ej(this,c,s,"f")}static fromSSEResponse(e,t,s){let n=!1,r=s?tE(s):console;async function*i(){if(n)throw new eB("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let s=!1;try{for await(let n of tN(e,t))if(!s){if(n.data.startsWith("[DONE]")){s=!0;continue}if(null===n.event||n.event.startsWith("response.")||n.event.startsWith("transcript.")){let t;try{t=JSON.parse(n.data)}catch(e){throw r.error("Could not parse message into JSON:",n.data),r.error("From chunk:",n.raw),e}if(t&&t.error)throw new eq(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(n.data)}catch(e){throw console.error("Could not parse message into JSON:",n.data),console.error("From chunk:",n.raw),e}if("error"==n.event)throw new eq(void 0,e.error,e.message,void 0);yield{event:n.event,data:e}}}s=!0}catch(e){if(eL(e))return;throw e}finally{s||t.abort()}}return new tC(i,t,s)}static fromReadableStream(e,t,s){let n=!1;async function*r(){let t=new tS;for await(let s of tl(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tC(async function*(){if(n)throw new eB("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eL(e))return;throw e}finally{e||t.abort()}},t,s)}[(c=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new tC(()=>n(e),this.controller,eM(this,c,"f")),new tC(()=>n(t),this.controller,eM(this,c,"f"))]}toReadableStream(){let e,t=this;return ta({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:n}=await e.next();if(n)return t.close();let r=tv(JSON.stringify(s)+"\n");t.enqueue(r)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tN(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eB("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eB("Attempted to iterate over a response with no body")}let s=new tj,n=new tS;for await(let t of tT(tl(e.body)))for(let e of n.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of n.flush()){let t=s.decode(e);t&&(yield t)}}async function*tT(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let n=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tv(s):s,r=new Uint8Array(t.length+n.length);for(r.set(t),r.set(n,t.length),t=r;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tj{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}async function tM(e,t){let{response:s,requestLogID:n,retryOfRequestLogID:r,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(tE(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller,e):tC.fromSSEResponse(s,t.controller,e);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let n=s.headers.get("content-type"),r=n?.split(";")[0]?.trim();return r?.includes("application/json")||r?.endsWith("+json")?tD(await s.json(),s):await s.text()})();return tE(e).debug(`[${n}] response parsed`,tP({retryOfRequestLogID:r,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function tD(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tL extends Promise{constructor(e,t,s=tM){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,u.set(this,void 0),ej(this,u,e,"f")}_thenUnwrap(e){return new tL(eM(this,u,"f"),this.responsePromise,async(t,s)=>tD(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eM(this,u,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}u=new WeakMap;class tU{constructor(e,t,s,n){h.set(this,void 0),ej(this,h,e,"f"),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eB("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eM(this,h,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(h=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tB extends tL{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tM(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tq extends tU{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tW extends tU{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let tF=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tH(e,t,s){return tF(),new File(e,t??"unknown_file",s)}function tX(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tJ=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tK=async(e,t)=>({...e,body:await tV(e.body,t)}),tz=new WeakMap,tV=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tz.get(t);if(s)return s;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tz.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tZ(s,e,t))),s},tY=e=>e instanceof Blob&&"name"in e,tQ=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tJ(e)||tY(e)),tG=e=>{if(tQ(e))return!0;if(Array.isArray(e))return e.some(tG);if(e&&"object"==typeof e){for(let t in e)if(tG(e[t]))return!0}return!1},tZ=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tH([await s.blob()],tX(s)));else if(tJ(s))e.append(t,tH([await new Response(to(s)).blob()],tX(s)));else if(tY(s))e.append(t,s,tX(s));else if(Array.isArray(s))await Promise.all(s.map(s=>tZ(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,n])=>tZ(e,`${t}[${s}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},t0=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,t1=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&t0(e),t2=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function t3(e,t,s){if(tF(),t1(e=await e))return e instanceof File?e:tH([await e.arrayBuffer()],e.name);if(t2(e)){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tH(await t4(n),t,s)}let n=await t4(e);if(t||(t=tX(e)),!s?.type){let e=n.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tH(n,t,s)}async function t4(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(t0(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tJ(e))for await(let s of e)t.push(...await t4(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class t6{constructor(e){this._client=e}}function t8(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t5=Object.freeze(Object.create(null)),t9=((e=t8)=>function(t,...s){let n;if(1===t.length)return t[0];let r=!1,i=[],a=t.reduce((t,n,a)=>{/[?#]/.test(n)&&(r=!0);let o=s[a],l=(r?encodeURIComponent:e)(""+o);return a!==s.length&&(null==o||"object"==typeof o&&o.toString===Object.getPrototypeOf(Object.getPrototypeOf(o.hasOwnProperty??t5)??t5)?.toString)&&(l=o+"",i.push({start:t.length+n.length,length:l.length,error:`Value of type ${Object.prototype.toString.call(o).slice(8,-1)} is not a valid path parameter`})),t+n+(a===s.length?"":l)},""),o=a.split(/[?#]/,1)[0],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(o));)i.push({start:n.index,length:n[0].length,error:`Value "${n[0]}" can't be safely passed as a path parameter`});if(i.sort((e,t)=>e.start-t.start),i.length>0){let e=0,t=i.reduce((t,s)=>{let n=" ".repeat(s.start-e),r="^".repeat(s.length);return e=s.start+s.length,t+n+r},"");throw new eB(`Path parameters result in path with invalid segments:
${i.map(e=>e.error).join("\n")}
${a}
${t}`)}return a})(t8);class t7 extends t6{list(e,t={},s){return this._client.getAPIList(t9`/chat/completions/${e}/messages`,tW,{query:t,...s})}}let se=e=>e?.role==="assistant",st=e=>e?.role==="tool";class ss{constructor(){d.add(this),this.controller=new AbortController,p.set(this,void 0),f.set(this,()=>{}),m.set(this,()=>{}),g.set(this,void 0),y.set(this,()=>{}),w.set(this,()=>{}),_.set(this,{}),b.set(this,!1),v.set(this,!1),x.set(this,!1),S.set(this,!1),ej(this,p,new Promise((e,t)=>{ej(this,f,e,"f"),ej(this,m,t,"f")}),"f"),ej(this,g,new Promise((e,t)=>{ej(this,y,e,"f"),ej(this,w,t,"f")}),"f"),eM(this,p,"f").catch(()=>{}),eM(this,g,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eM(this,d,"m",A).bind(this))},0)}_connected(){this.ended||(eM(this,f,"f").call(this),this._emit("connect"))}get ended(){return eM(this,b,"f")}get errored(){return eM(this,v,"f")}get aborted(){return eM(this,x,"f")}abort(){this.controller.abort()}on(e,t){return(eM(this,_,"f")[e]||(eM(this,_,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eM(this,_,"f")[e];if(!s)return this;let n=s.findIndex(e=>e.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(eM(this,_,"f")[e]||(eM(this,_,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{ej(this,S,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){ej(this,S,!0,"f"),await eM(this,g,"f")}_emit(e,...t){if(eM(this,b,"f"))return;"end"===e&&(ej(this,b,!0,"f"),eM(this,y,"f").call(this));let s=eM(this,_,"f")[e];if(s&&(eM(this,_,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eM(this,S,"f")||s?.length||Promise.reject(e),eM(this,m,"f").call(this,e),eM(this,w,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eM(this,S,"f")||s?.length||Promise.reject(e),eM(this,m,"f").call(this,e),eM(this,w,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function sn(e){return e?.$brand==="auto-parseable-response-format"}function sr(e){return e?.$brand==="auto-parseable-tool"}function si(e,t){let s=e.choices.map(e=>{var s,n;if("length"===e.finish_reason)throw new eZ;if("content_filter"===e.finish_reason)throw new e0;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:sr(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,n=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:s}}function sa(e){return!!sn(e.response_format)||(e.tools?.some(e=>sr(e)||"function"===e.type&&!0===e.function.strict)??!1)}p=new WeakMap,f=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,b=new WeakMap,v=new WeakMap,x=new WeakMap,S=new WeakMap,d=new WeakSet,A=function(e){if(ej(this,v,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eW),e instanceof eW)return ej(this,x,!0,"f"),this._emit("abort",e);if(e instanceof eB)return this._emit("error",e);if(e instanceof Error){let t=new eB(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eB(String(e)))};class so extends ss{constructor(){super(...arguments),I.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),st(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(se(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eB("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eM(this,I,"m",$).call(this)}async finalMessage(){return await this.done(),eM(this,I,"m",O).call(this)}async finalFunctionToolCall(){return await this.done(),eM(this,I,"m",k).call(this)}async finalFunctionToolCallResult(){return await this.done(),eM(this,I,"m",R).call(this)}async totalUsage(){return await this.done(),eM(this,I,"m",E).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eM(this,I,"m",O).call(this);t&&this._emit("finalMessage",t);let s=eM(this,I,"m",$).call(this);s&&this._emit("finalContent",s);let n=eM(this,I,"m",k).call(this);n&&this._emit("finalFunctionToolCall",n);let r=eM(this,I,"m",R).call(this);null!=r&&this._emit("finalFunctionToolCallResult",r),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eM(this,I,"m",E).call(this))}async _createChatCompletion(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eM(this,I,"m",P).call(this,t);let r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(si(r,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let n="tool",{tool_choice:r="auto",stream:i,...a}=t,o="string"!=typeof r&&r?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(sr(e)){if(!e.$callback)throw new eB("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:r,tools:h,messages:[...this.messages]},s),i=t.choices[0]?.message;if(!i)throw new eB("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:r,arguments:i}=e.function,a=u[r];if(a){if(o&&o!==r){let e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:s,content:e});continue}let l=await a.function(t,this),c=eM(this,I,"m",C).call(this,l);if(this._addMessage({role:n,tool_call_id:s,content:c}),o)return}}}}I=new WeakSet,$=function(){return eM(this,I,"m",O).call(this).content??null},O=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(se(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eB("stream ended without producing a ChatCompletionMessage with role=assistant")},k=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(se(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},R=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(st(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},E=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},P=function(e){if(null!=e.n&&e.n>1)throw new eB("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},C=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class sl extends so{static runTools(e,t,s){let n=new sl,r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}_addMessage(e,t=!0){super._addMessage(e,t),se(e)&&e.content&&this._emit("content",e.content)}}let sc={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class su extends Error{}class sh extends Error{}let sd=(e,t)=>{let s=e.length,n=0,r=e=>{throw new su(`${e} at position ${n}`)},i=e=>{throw new sh(`${e} at position ${n}`)},a=()=>(h(),n>=s&&r("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||sc.NULL&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||sc.BOOL&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||sc.BOOL&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||sc.INFINITY&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||sc.MINUS_INFINITY&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||sc.NAN&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let a=n,o=!1;for(n++;n<s&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(a,++n-Number(o)))}catch(e){i(String(e))}else if(sc.STR&t)try{return JSON.parse(e.substring(a,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,h();let i={};try{for(;"}"!==e[n];){if(h(),n>=s&&sc.OBJ&t)return i;let r=o();h(),n++;try{let e=a();Object.defineProperty(i,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(sc.OBJ&t)return i;throw e}h(),","===e[n]&&n++}}catch(e){if(sc.OBJ&t)return i;r("Expected '}' at end of object")}return n++,i},c=()=>{n++;let s=[];try{for(;"]"!==e[n];)s.push(a()),h(),","===e[n]&&n++}catch(e){if(sc.ARR&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&sc.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(sc.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(s))}}let a=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||sc.NUM&t||r("Unterminated number literal");try{return JSON.parse(e.substring(a,n))}catch(s){"-"===e.substring(a,n)&&sc.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;n<s&&" \n\r	".includes(e[n]);)n++};return a()},sp=e=>(function(e,t=sc.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return sd(e.trim(),t)})(e,sc.ALL^sc.NUM);class sf extends so{constructor(e){super(),N.add(this),T.set(this,void 0),j.set(this,void 0),M.set(this,void 0),ej(this,T,e,"f"),ej(this,j,[],"f")}get currentChatCompletionSnapshot(){return eM(this,M,"f")}static fromReadableStream(e){let t=new sf(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let n=new sf(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,s){super._createChatCompletion;let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eM(this,N,"m",D).call(this);let r=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),r))eM(this,N,"m",U).call(this,e);if(r.controller.signal?.aborted)throw new eW;return this._addChatCompletion(eM(this,N,"m",W).call(this))}async _fromReadableStream(e,t){let s,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eM(this,N,"m",D).call(this),this._connected();let r=tC.fromReadableStream(e,this.controller);for await(let e of r)s&&s!==e.id&&this._addChatCompletion(eM(this,N,"m",W).call(this)),eM(this,N,"m",U).call(this,e),s=e.id;if(r.controller.signal?.aborted)throw new eW;return this._addChatCompletion(eM(this,N,"m",W).call(this))}[(T=new WeakMap,j=new WeakMap,M=new WeakMap,N=new WeakSet,D=function(){this.ended||ej(this,M,void 0,"f")},L=function(e){let t=eM(this,j,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eM(this,j,"f")[e.index]=t),t},U=function(e){if(this.ended)return;let t=eM(this,N,"m",H).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=eM(this,N,"m",L).call(this,e);for(let t of(e.finish_reason&&(eM(this,N,"m",q).call(this,e),null!=n.current_tool_call_index&&eM(this,N,"m",B).call(this,e,n.current_tool_call_index)),s.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(eM(this,N,"m",q).call(this,e),null!=n.current_tool_call_index&&eM(this,N,"m",B).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},B=function(e,t){if(eM(this,N,"m",L).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=eM(this,T,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:sr(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},q=function(e){let t=eM(this,N,"m",L).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=eM(this,N,"m",F).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},W=function(){if(this.ended)throw new eB("stream has ended, this shouldn't happen");let e=eM(this,M,"f");if(!e)throw new eB("request ended without sending any chunks");return ej(this,M,void 0,"f"),ej(this,j,[],"f"),function(e,t){var s;let{id:n,choices:r,created:i,model:a,system_fingerprint:o,...l}=e;return s={...l,id:n,choices:r.map(({message:t,finish_reason:s,index:n,logprobs:r,...i})=>{if(!s)throw new eB(`missing finish_reason for choice ${n}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new eB(`missing role for choice ${n}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eB(`missing function_call.arguments for choice ${n}`);if(!l)throw new eB(`missing function_call.name for choice ${n}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...i,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:r,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=r||{};if(null==a)throw new eB(`missing choices[${n}].tool_calls[${s}].id
${sm(e)}`);if(null==i)throw new eB(`missing choices[${n}].tool_calls[${s}].type
${sm(e)}`);if(null==c)throw new eB(`missing choices[${n}].tool_calls[${s}].function.name
${sm(e)}`);if(null==l)throw new eB(`missing choices[${n}].tool_calls[${s}].function.arguments
${sm(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&sa(t)?si(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eM(this,T,"f"))},F=function(){let e=eM(this,T,"f")?.response_format;return sn(e)?e:null},H=function(e){var t,s,n,r;let i=eM(this,M,"f"),{choices:a,...o}=e;for(let{delta:a,finish_reason:l,index:c,logprobs:u=null,...h}of(i?Object.assign(i,o):i=ej(this,M,{...o,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:n,refusal:r,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,eM(this,T,"f")&&sa(eM(this,T,"f")))){if("length"===l)throw new eZ;if("content_filter"===l)throw new e0}if(Object.assign(e,h),!a)continue;let{content:o,refusal:d,function_call:p,role:f,tool_calls:m,...g}=a;if(Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),f&&(e.message.role=f),p&&(e.message.function_call?(p.name&&(e.message.function_call.name=p.name),p.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=p.arguments)):e.message.function_call=p),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eM(this,N,"m",F).call(this)&&(e.message.parsed=sp(e.message.content))),m)for(let{index:t,id:s,type:n,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(o,a),s&&(o.id=s),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return sr(s)||s?.function.strict||!1}(eM(this,T,"f"),o)&&(o.function.parsed_arguments=sp(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tC(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function sm(e){return JSON.stringify(e)}class sg extends sf{static fromReadableStream(e){let t=new sg(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let n=new sg(t),r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}}class sy extends t6{constructor(){super(...arguments),this.messages=new t7(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(t9`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(t9`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eB(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eB(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>si(t,e))}runTools(e,t){return e.stream?sg.runTools(this._client,e,t):sl.runTools(this._client,e,t)}stream(e,t){return sf.createChatCompletion(this._client,e,t)}}sy.Messages=t7;class sw extends t6{constructor(){super(...arguments),this.completions=new sy(this._client)}}sw.Completions=sy;let s_=Symbol("brand.privateNullableHeaders"),sb=e=>{let t=new Headers,s=new Set;for(let n of e){let e=new Set;for(let[r,i]of function*(e){let t;if(!e)return;if(s_ in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let n of(e instanceof Headers?t=e.entries():e6(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=e6(n[1])?n[1]:[n[1]],r=!1;for(let n of t)void 0!==n&&(s&&!r&&(r=!0,yield[e,null]),yield[e,n])}}(n)){let n=r.toLowerCase();e.has(n)||(t.delete(r),e.add(n)),null===i?(t.delete(r),s.add(n)):(t.append(r,i),s.delete(n))}}return{[s_]:!0,values:t,nulls:s}};class sv extends t6{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:sb([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sx extends t6{create(e,t){return this._client.post("/audio/transcriptions",tK({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class sS extends t6{create(e,t){return this._client.post("/audio/translations",tK({body:e,...t,__metadata:{model:e.model}},this._client))}}class sA extends t6{constructor(){super(...arguments),this.transcriptions=new sx(this._client),this.translations=new sS(this._client),this.speech=new sv(this._client)}}sA.Transcriptions=sx,sA.Translations=sS,sA.Speech=sv;class sI extends t6{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tW,{query:e,...t})}cancel(e,t){return this._client.post(t9`/batches/${e}/cancel`,t)}}class s$ extends t6{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t9`/assistants/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t9`/assistants/${e}`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tW,{query:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t9`/assistants/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sO extends t6{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sk extends t6{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sR extends t6{constructor(){super(...arguments),this.sessions=new sO(this._client),this.transcriptionSessions=new sk(this._client)}}sR.Sessions=sO,sR.TranscriptionSessions=sk;class sE extends t6{create(e,t,s){return this._client.post(t9`/threads/${e}/messages`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t9`/threads/${n}/messages/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t9`/threads/${n}/messages/${e}`,{body:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t9`/threads/${e}/messages`,tW,{query:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:n}=t;return this._client.delete(t9`/threads/${n}/messages/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class sP extends t6{retrieve(e,t,s){let{thread_id:n,run_id:r,...i}=t;return this._client.get(t9`/threads/${n}/runs/${r}/steps/${e}`,{query:i,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:n,...r}=t;return this._client.getAPIList(t9`/threads/${n}/runs/${e}/steps`,tW,{query:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sC=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}},sN=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sT extends ss{constructor(){super(...arguments),X.add(this),K.set(this,[]),z.set(this,{}),V.set(this,{}),Y.set(this,void 0),Q.set(this,void 0),G.set(this,void 0),Z.set(this,void 0),ee.set(this,void 0),et.set(this,void 0),es.set(this,void 0),en.set(this,void 0),er.set(this,void 0)}[(K=new WeakMap,z=new WeakMap,V=new WeakMap,Y=new WeakMap,Q=new WeakMap,G=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,es=new WeakMap,en=new WeakMap,er=new WeakMap,X=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new J;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tC.fromReadableStream(e,this.controller);for await(let e of n)eM(this,X,"m",ei).call(this,e);if(n.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,X,"m",ea).call(this))}toReadableStream(){return new tC(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n){let r=new J;return r._run(()=>r._runToolAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createToolAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.submitToolOutputs(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eM(this,X,"m",ei).call(this,e);if(a.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,X,"m",ea).call(this))}static createThreadAssistantStream(e,t,s){let n=new J;return n._run(()=>n._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,s,n){let r=new J;return r._run(()=>r._runAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}currentEvent(){return eM(this,es,"f")}currentRun(){return eM(this,en,"f")}currentMessageSnapshot(){return eM(this,Y,"f")}currentRunStepSnapshot(){return eM(this,er,"f")}async finalRunSteps(){return await this.done(),Object.values(eM(this,z,"f"))}async finalMessages(){return await this.done(),Object.values(eM(this,V,"f"))}async finalRun(){if(await this.done(),!eM(this,Q,"f"))throw Error("Final run was not received.");return eM(this,Q,"f")}async _createThreadAssistantStream(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let r={...t,stream:!0},i=await e.createAndRun(r,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))eM(this,X,"m",ei).call(this,e);if(i.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,X,"m",ea).call(this))}async _createAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eM(this,X,"m",ei).call(this,e);if(a.controller.signal?.aborted)throw new eW;return this._addRun(eM(this,X,"m",ea).call(this))}static accumulateDelta(e,t){for(let[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(e8(t)&&e8(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!e8(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n){return await this._createToolAssistantStream(t,e,s,n)}}J=sT,ei=function(e){if(!this.ended)switch(ej(this,es,e,"f"),eM(this,X,"m",ec).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eM(this,X,"m",ep).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eM(this,X,"m",el).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eM(this,X,"m",eo).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ea=function(){if(this.ended)throw new eB("stream has ended, this shouldn't happen");if(!eM(this,Q,"f"))throw Error("Final run has not been received");return eM(this,Q,"f")},eo=function(e){let[t,s]=eM(this,X,"m",eh).call(this,e,eM(this,Y,"f"));for(let e of(ej(this,Y,t,"f"),eM(this,V,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=eM(this,G,"f")){if(eM(this,Z,"f"))switch(eM(this,Z,"f").type){case"text":this._emit("textDone",eM(this,Z,"f").text,eM(this,Y,"f"));break;case"image_file":this._emit("imageFileDone",eM(this,Z,"f").image_file,eM(this,Y,"f"))}ej(this,G,s.index,"f")}ej(this,Z,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eM(this,G,"f")){let t=e.data.content[eM(this,G,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eM(this,Y,"f"));break;case"text":this._emit("textDone",t.text,eM(this,Y,"f"))}}eM(this,Y,"f")&&this._emit("messageDone",e.data),ej(this,Y,void 0,"f")}},el=function(e){let t=eM(this,X,"m",eu).call(this,e);switch(ej(this,er,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==eM(this,ee,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eM(this,et,"f")&&this._emit("toolCallDone",eM(this,et,"f")),ej(this,ee,e.index,"f"),ej(this,et,t.step_details.tool_calls[e.index],"f"),eM(this,et,"f")&&this._emit("toolCallCreated",eM(this,et,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ej(this,er,void 0,"f"),"tool_calls"==e.data.step_details.type&&eM(this,et,"f")&&(this._emit("toolCallDone",eM(this,et,"f")),ej(this,et,void 0,"f")),this._emit("runStepDone",e.data,t)}},ec=function(e){eM(this,K,"f").push(e),this._emit("event",e)},eu=function(e){switch(e.event){case"thread.run.step.created":return eM(this,z,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eM(this,z,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let n=J.accumulateDelta(t,s.delta);eM(this,z,"f")[e.data.id]=n}return eM(this,z,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eM(this,z,"f")[e.data.id]=e.data}if(eM(this,z,"f")[e.data.id])return eM(this,z,"f")[e.data.id];throw Error("No snapshot available")},eh=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=eM(this,X,"m",ed).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},ed=function(e,t){return J.accumulateDelta(t,e)},ep=function(e){switch(ej(this,en,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":ej(this,Q,e.data,"f"),eM(this,et,"f")&&(this._emit("toolCallDone",eM(this,et,"f")),ej(this,et,void 0,"f"))}};class sj extends t6{constructor(){super(...arguments),this.steps=new sP(this._client)}create(e,t,s){let{include:n,...r}=t;return this._client.post(t9`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t9`/threads/${n}/runs/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t9`/threads/${n}/runs/${e}`,{body:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t9`/threads/${e}/runs`,tW,{query:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:n}=t;return this._client.post(t9`/threads/${n}/runs/${e}/cancel`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(n.id,{thread_id:e},s)}createAndStream(e,t,s){return sT.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let n=sb([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e7(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return sT.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t9`/threads/${n}/runs/${e}/submit_tool_outputs`,{body:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let n=await this.submitToolOutputs(e,t,s);return await this.poll(n.id,t,s)}submitToolOutputsStream(e,t,s){return sT.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sj.Steps=sP;class sM extends t6{constructor(){super(...arguments),this.runs=new sj(this._client),this.messages=new sE(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t9`/threads/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t9`/threads/${e}`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(t9`/threads/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sT.createThreadAssistantStream(e,this._client.beta.threads,t)}}sM.Runs=sj,sM.Messages=sE;class sD extends t6{constructor(){super(...arguments),this.realtime=new sR(this._client),this.assistants=new s$(this._client),this.threads=new sM(this._client)}}sD.Realtime=sR,sD.Assistants=s$,sD.Threads=sM;class sL extends t6{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sU extends t6{retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t9`/containers/${n}/files/${e}/content`,{...s,headers:sb([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sB extends t6{constructor(){super(...arguments),this.content=new sU(this._client)}create(e,t,s){return this._client.post(t9`/containers/${e}/files`,tK({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t9`/containers/${n}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t9`/containers/${e}/files`,tW,{query:t,...s})}delete(e,t,s){let{container_id:n}=t;return this._client.delete(t9`/containers/${n}/files/${e}`,{...s,headers:sb([{Accept:"*/*"},s?.headers])})}}sB.Content=sU;class sq extends t6{constructor(){super(...arguments),this.files=new sB(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/containers/${e}`,{...t,headers:sb([{Accept:"*/*"},t?.headers])})}}sq.Files=sB;class sW extends t6{create(e,t){let s=!!e.encoding_format,n=s?e.encoding_format:"base64";s&&tE(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(tE(this._client).debug("embeddings/decoding base64 embeddings from base64"),r._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=sC(t)}),e)))}}class sF extends t6{retrieve(e,t,s){let{eval_id:n,run_id:r}=t;return this._client.get(t9`/evals/${n}/runs/${r}/output_items/${e}`,s)}list(e,t,s){let{eval_id:n,...r}=t;return this._client.getAPIList(t9`/evals/${n}/runs/${e}/output_items`,tW,{query:r,...s})}}class sH extends t6{constructor(){super(...arguments),this.outputItems=new sF(this._client)}create(e,t,s){return this._client.post(t9`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:n}=t;return this._client.get(t9`/evals/${n}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t9`/evals/${e}/runs`,tW,{query:t,...s})}delete(e,t,s){let{eval_id:n}=t;return this._client.delete(t9`/evals/${n}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:n}=t;return this._client.post(t9`/evals/${n}/runs/${e}`,s)}}sH.OutputItems=sF;class sX extends t6{constructor(){super(...arguments),this.runs=new sH(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/evals/${e}`,t)}update(e,t,s){return this._client.post(t9`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/evals/${e}`,t)}}sX.Runs=sH;class sJ extends t6{create(e,t){return this._client.post("/files",tK({body:e,...t},this._client))}retrieve(e,t){return this._client.get(t9`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tW,{query:e,...t})}delete(e,t){return this._client.delete(t9`/files/${e}`,t)}content(e,t){return this._client.get(t9`/files/${e}/content`,{...t,headers:sb([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let n=new Set(["processed","error","deleted"]),r=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await e7(t),i=await this.retrieve(e),Date.now()-r>s)throw new eH({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}}class sK extends t6{}class sz extends t6{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sV extends t6{constructor(){super(...arguments),this.graders=new sz(this._client)}}sV.Graders=sz;class sY extends t6{create(e,t,s){return this._client.getAPIList(t9`/fine_tuning/checkpoints/${e}/permissions`,tq,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(t9`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(t9`/fine_tuning/checkpoints/${n}/permissions/${e}`,s)}}class sQ extends t6{constructor(){super(...arguments),this.permissions=new sY(this._client)}}sQ.Permissions=sY;class sG extends t6{list(e,t={},s){return this._client.getAPIList(t9`/fine_tuning/jobs/${e}/checkpoints`,tW,{query:t,...s})}}class sZ extends t6{constructor(){super(...arguments),this.checkpoints=new sG(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(t9`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tW,{query:e,...t})}cancel(e,t){return this._client.post(t9`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(t9`/fine_tuning/jobs/${e}/events`,tW,{query:t,...s})}pause(e,t){return this._client.post(t9`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(t9`/fine_tuning/jobs/${e}/resume`,t)}}sZ.Checkpoints=sG;class s0 extends t6{constructor(){super(...arguments),this.methods=new sK(this._client),this.jobs=new sZ(this._client),this.checkpoints=new sQ(this._client),this.alpha=new sV(this._client)}}s0.Methods=sK,s0.Jobs=sZ,s0.Checkpoints=sQ,s0.Alpha=sV;class s1 extends t6{}class s2 extends t6{constructor(){super(...arguments),this.graderModels=new s1(this._client)}}s2.GraderModels=s1;class s3 extends t6{createVariation(e,t){return this._client.post("/images/variations",tK({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tK({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class s4 extends t6{retrieve(e,t){return this._client.get(t9`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tq,e)}delete(e,t){return this._client.delete(t9`/models/${e}`,t)}}class s6 extends t6{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function s8(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,n;return"output_text"===e.type?{...e,parsed:(s=t,n=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:s}}return e}),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||s5(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function s5(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class s9 extends ss{constructor(e){super(),ef.add(this),em.set(this,void 0),eg.set(this,void 0),ey.set(this,void 0),ej(this,em,e,"f")}static createResponse(e,t,s){let n=new s9(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,s){let n,r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eM(this,ef,"m",ew).call(this);let i=null;for await(let r of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),n))eM(this,ef,"m",e_).call(this,r,i);if(n.controller.signal?.aborted)throw new eW;return eM(this,ef,"m",eb).call(this)}[(em=new WeakMap,eg=new WeakMap,ey=new WeakMap,ef=new WeakSet,ew=function(){this.ended||ej(this,eg,void 0,"f")},e_=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},n=eM(this,ef,"m",ev).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new eB(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new eB(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new eB(`expected content to be 'output_text', got ${n.type}`);s("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new eB(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},eb=function(){if(this.ended)throw new eB("stream has ended, this shouldn't happen");let e=eM(this,eg,"f");if(!e)throw new eB("request ended without sending any events");ej(this,eg,void 0,"f");let t=function(e,t){var s;return t&&(s=t,sn(s.text?.format))?s8(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eM(this,em,"f"));return ej(this,ey,t,"f"),t},ev=function(e){let t=eM(this,eg,"f");if(!t){if("response.created"!==e.type)throw new eB(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return ej(this,eg,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new eB(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new eB(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new eB(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eB(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new eB(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":ej(this,eg,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eM(this,ey,"f");if(!e)throw new eB("stream ended without producing a ChatCompletion");return e}}class s7 extends t6{list(e,t={},s){return this._client.getAPIList(t9`/responses/${e}/input_items`,tW,{query:t,...s})}}class ne extends t6{constructor(){super(...arguments),this.inputItems=new s7(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s5(e),e))}retrieve(e,t={},s){return this._client.get(t9`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s5(e),e))}delete(e,t){return this._client.delete(t9`/responses/${e}`,{...t,headers:sb([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>s8(t,e))}stream(e,t){return s9.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(t9`/responses/${e}/cancel`,t)}}ne.InputItems=s7;class nt extends t6{create(e,t,s){return this._client.post(t9`/uploads/${e}/parts`,tK({body:t,...s},this._client))}}class ns extends t6{constructor(){super(...arguments),this.parts=new nt(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(t9`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(t9`/uploads/${e}/complete`,{body:t,...s})}}ns.Parts=nt;let nn=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class nr extends t6{create(e,t,s){return this._client.post(t9`/vector_stores/${e}/file_batches`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t9`/vector_stores/${n}/file_batches/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:n}=t;return this._client.post(t9`/vector_stores/${n}/file_batches/${e}/cancel`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s){let{vector_store_id:n,...r}=t;return this._client.getAPIList(t9`/vector_stores/${n}/file_batches/${e}/files`,tW,{query:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let n=sb([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e7(a);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let r=Math.min(n?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...s];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(r).fill(a).map(l);return await nn(c),await this.createAndPoll(e,{file_ids:o})}}class ni extends t6{create(e,t,s){return this._client.post(t9`/vector_stores/${e}/files`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t9`/vector_stores/${n}/files/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:n,...r}=t;return this._client.post(t9`/vector_stores/${n}/files/${e}`,{body:r,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t9`/vector_stores/${e}/files`,tW,{query:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:n}=t;return this._client.delete(t9`/vector_stores/${n}/files/${e}`,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){let n=sb([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let r=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse(),i=r.data;switch(i.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=r.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e7(a);break;case"failed":case"completed":return i}}}async upload(e,t,s){let n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){let n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){let{vector_store_id:n}=t;return this._client.getAPIList(t9`/vector_stores/${n}/files/${e}/content`,tq,{...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class na extends t6{constructor(){super(...arguments),this.files=new ni(this._client),this.fileBatches=new nr(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t9`/vector_stores/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t9`/vector_stores/${e}`,{body:t,...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tW,{query:e,...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t9`/vector_stores/${e}`,{...t,headers:sb([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(t9`/vector_stores/${e}/search`,tq,{body:t,method:"post",...s,headers:sb([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}na.Files=ni,na.FileBatches=nr;class no extends t6{constructor(){super(...arguments),ex.add(this)}async unwrap(e,t,s=this._client.webhookSecret,n=300){return await this.verifySignature(e,t,s,n),JSON.parse(e)}async verifySignature(e,t,s=this._client.webhookSecret,n=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");eM(this,ex,"m",eS).call(this,s);let r=sb([t]).values,i=eM(this,ex,"m",eA).call(this,r,"webhook-signature"),a=eM(this,ex,"m",eA).call(this,r,"webhook-timestamp"),o=eM(this,ex,"m",eA).call(this,r,"webhook-id"),l=parseInt(a,10);if(isNaN(l))throw new e1("Invalid webhook timestamp format");let c=Math.floor(Date.now()/1e3);if(c-l>n)throw new e1("Webhook timestamp is too old");if(l>c+n)throw new e1("Webhook timestamp is too new");let u=i.split(" ").map(e=>e.startsWith("v1,")?e.substring(3):e),h=s.startsWith("whsec_")?Buffer.from(s.replace("whsec_",""),"base64"):Buffer.from(s,"utf-8"),d=o?`${o}.${a}.${e}`:`${a}.${e}`,p=await crypto.subtle.importKey("raw",h,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let e of u)try{let t=Buffer.from(e,"base64");if(await crypto.subtle.verify("HMAC",p,t,new TextEncoder().encode(d)))return}catch{continue}throw new e1("The given webhook signature does not match the expected signature")}}ex=new WeakSet,eS=function(e){if("string"!=typeof e||0===e.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},eA=function(e,t){if(!e)throw Error("Headers are required");let s=e.get(t);if(null==s)throw Error(`Missing required header: ${t}`);return s};class nl{constructor({baseURL:e=sN("OPENAI_BASE_URL"),apiKey:t=sN("OPENAI_API_KEY"),organization:s=sN("OPENAI_ORG_ID")??null,project:n=sN("OPENAI_PROJECT_ID")??null,webhookSecret:r=sN("OPENAI_WEBHOOK_SECRET")??null,...i}={}){if(eI.add(this),eO.set(this,void 0),this.completions=new sL(this),this.chat=new sw(this),this.embeddings=new sW(this),this.files=new sJ(this),this.images=new s3(this),this.audio=new sA(this),this.moderations=new s6(this),this.models=new s4(this),this.fineTuning=new s0(this),this.graders=new s2(this),this.vectorStores=new na(this),this.webhooks=new no(this),this.beta=new sD(this),this.batches=new sI(this),this.uploads=new ns(this),this.responses=new ne(this),this.evals=new sX(this),this.containers=new sq(this),void 0===t)throw new eB("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let a={apiKey:t,organization:s,project:n,webhookSecret:r,...i,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&tt())throw new eB("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=a.baseURL,this.timeout=a.timeout??e$.DEFAULT_TIMEOUT,this.logger=a.logger??console;let o="warn";this.logLevel=o,this.logLevel=tI(a.logLevel,"ClientOptions.logLevel",this)??tI(sN("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??o,this.fetchOptions=a.fetchOptions,this.maxRetries=a.maxRetries??2,this.fetch=a.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ej(this,eO,tu,"f"),this._options=a,this.apiKey=t,this.organization=s,this.project=n,this.webhookSecret=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}async authHeaders(e){return sb([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,n,r=e,i=function(e=t_){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||t_.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=th;if(void 0!==e.format){if(!tf(tp,e.format))throw TypeError("Unknown format option provided.");n=e.format}let r=tp[n],i=t_.filter;if(("function"==typeof e.filter||e4(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in ty?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":t_.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||t_.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:t_.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:t_.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:t_.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?t_.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:t_.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:t_.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:t_.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:t_.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:t_.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:t_.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:t_.strictNullHandling}}(t);"function"==typeof i.filter?r=(0,i.filter)("",r):e4(i.filter)&&(s=i.filter);let a=[];if("object"!=typeof r||null===r)return"";let o=ty[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;s||(s=Object.keys(r)),i.sort&&s.sort(i.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];i.skipNulls&&null===r[t]||tw(a,function e(t,s,n,r,i,a,o,l,c,u,h,d,p,f,m,g,y,w){var _,b;let v,x=t,S=w,A=0,I=!1;for(;void 0!==(S=S.get(tb))&&!I;){let e=S.get(t);if(A+=1,void 0!==e)if(e===A)throw RangeError("Cyclic object value");else I=!0;void 0===S.get(tb)&&(A=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=p?.(x):"comma"===n&&e4(x)&&(x=tg(x,function(e){return e instanceof Date?p?.(e):e})),null===x){if(a)return c&&!g?c(s,t_.encoder,y,"key",f):s;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,t_.encoder,y,"key",f);return[m?.(e)+"="+m?.(c(x,t_.encoder,y,"value",f))]}return[m?.(s)+"="+m?.(String(x))]}let $=[];if(void 0===x)return $;if("comma"===n&&e4(x))g&&c&&(x=tg(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(e4(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let O=l?String(s).replace(/\./g,"%2E"):String(s),k=r&&e4(x)&&1===x.length?O+"[]":O;if(i&&e4(x)&&0===x.length)return k+"[]";for(let s=0;s<v.length;++s){let _=v[s],b="object"==typeof _&&void 0!==_.value?_.value:x[_];if(o&&null===b)continue;let S=d&&l?_.replace(/\./g,"%2E"):_,I=e4(x)?"function"==typeof n?n(k,S):k:k+(d?"."+S:"["+S+"]");w.set(t,A);let O=new WeakMap;O.set(tb,w),tw($,e(b,I,n,r,i,a,o,l,"comma"===n&&g&&e4(x)?null:c,u,h,d,p,f,m,g,y,O))}return $}(r[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=a.join(i.delimiter),h=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${te}`}defaultIdempotencyKey(){return`stainless-node-retry-${eD()}`}makeStatusError(e,t,s,n){return eq.generate(e,t,s,n)}buildURL(e,t,s){let n=!eM(this,eI,"m",ek).call(this)&&s||this.baseURL,r=new URL(e3(e)?e:n+(n.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),i=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(i)&&(t={...i,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tL(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let n=await e,r=n.maxRetries??this.maxRetries;null==t&&(t=r),await this.prepareOptions(n);let{req:i,url:a,timeout:o}=await this.buildRequest(n,{retryCount:r-t});await this.prepareRequest(i,{url:a,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(tE(this).debug(`[${l}] sending request`,tP({retryOfRequestLogID:s,method:n.method,url:a,options:n,headers:i.headers})),n.signal?.aborted)throw new eW;let h=new AbortController,d=await this.fetchWithTimeout(a,i,o,h).catch(eU),p=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new eW;let r=eL(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return tE(this).info(`[${l}] connection ${r?"timed out":"failed"} - ${e}`),tE(this).debug(`[${l}] connection ${r?"timed out":"failed"} (${e})`,tP({retryOfRequestLogID:s,url:a,durationMs:p-u,message:d.message})),this.retryRequest(n,t,s??l);if(tE(this).info(`[${l}] connection ${r?"timed out":"failed"} - error; no more retries left`),tE(this).debug(`[${l}] connection ${r?"timed out":"failed"} (error; no more retries left)`,tP({retryOfRequestLogID:s,url:a,durationMs:p-u,message:d.message})),r)throw new eH;throw new eF({cause:d})}let f=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${f}] ${i.method} ${a} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${p-u}ms`;if(!d.ok){let e=await this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tc(d.body),tE(this).info(`${m} - ${e}`),tE(this).debug(`[${l}] response error (${e})`,tP({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:p-u})),this.retryRequest(n,t,s??l,d.headers)}let r=e?"error; no more retries left":"error; not retryable";tE(this).info(`${m} - ${r}`);let i=await d.text().catch(e=>eU(e).message),a=e9(i),o=a?void 0:i;throw tE(this).debug(`[${l}] response error (${r})`,tP({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,a,o,d.headers)}return tE(this).info(m),tE(this).debug(`[${l}] response start`,tP({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:p-u})),{response:d,options:n,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tB(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,n){let{signal:r,method:i,...a}=t||{};r&&r.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:n.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}async shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,n){let r,i=n?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(r=e)}let a=n?.get("retry-after");if(a&&!r){let e=parseFloat(a);r=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(r&&0<=r&&r<6e4)){let s=e.maxRetries??this.maxRetries;r=this.calculateDefaultRetryTimeoutMillis(t,s)}return await e7(r),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}async buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:n,path:r,query:i,defaultBaseURL:a}=s,o=this.buildURL(r,i,a);"timeout"in s&&e5("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:l,body:c}=this.buildBody({options:s}),u=await this.buildHeaders({options:e,method:n,bodyHeaders:l,retryCount:t});return{req:{method:n,headers:u,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&c instanceof globalThis.ReadableStream&&{duplex:"half"},...c&&{body:c},...this.fetchOptions??{},...s.fetchOptions??{}},url:o,timeout:s.timeout}}async buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:n}){let r={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),r[this.idempotencyHeader]=e.idempotencyKey);let i=sb([r,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...ti(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sb([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:to(e)}:eM(this,eO,"f").call(this,{body:e,headers:s})}}e$=nl,eO=new WeakMap,eI=new WeakSet,ek=function(){return"https://api.openai.com/v1"!==this.baseURL},nl.OpenAI=e$,nl.DEFAULT_TIMEOUT=6e5,nl.OpenAIError=eB,nl.APIError=eq,nl.APIConnectionError=eF,nl.APIConnectionTimeoutError=eH,nl.APIUserAbortError=eW,nl.NotFoundError=ez,nl.ConflictError=eV,nl.RateLimitError=eQ,nl.BadRequestError=eX,nl.AuthenticationError=eJ,nl.InternalServerError=eG,nl.PermissionDeniedError=eK,nl.UnprocessableEntityError=eY,nl.InvalidWebhookSignatureError=e1,nl.toFile=t3,nl.Completions=sL,nl.Chat=sw,nl.Embeddings=sW,nl.Files=sJ,nl.Images=s3,nl.Audio=sA,nl.Moderations=s6,nl.Models=s4,nl.FineTuning=s0,nl.Graders=s2,nl.VectorStores=na,nl.Webhooks=no,nl.Beta=sD,nl.Batches=sI,nl.Uploads=ns,nl.Responses=ne,nl.Evals=sX,nl.Containers=sq,console.log("OPENAI_API_KEY exists:",!!process.env.OPENAI_API_KEY),console.log("OPENAI_API_KEY length:",process.env.OPENAI_API_KEY?.length||0);let nc=process.env.OPENAI_API_KEY;if(!nc)throw Error("OPENAI_API_KEY environment variable is missing or empty");let nu=new nl({apiKey:nc}),nh={low:{name:"Basic Analysis",description:"Quick age estimation with basic explanation",credits:1,model:"gpt-4o-mini",imageDetail:"low",maxTokens:300,promptComplexity:"basic"},medium:{name:"Detailed Analysis",description:"Comprehensive age analysis with scientific explanation",credits:2,model:"gpt-4o",imageDetail:"high",maxTokens:500,promptComplexity:"detailed"},high:{name:"Expert Analysis",description:"In-depth professional analysis with multiple factors",credits:3,model:"gpt-4o",imageDetail:"high",maxTokens:800,promptComplexity:"comprehensive"}};async function nd(e,t="medium"){try{let s=nh[t],n=function(e){let t=`Look carefully at this image and analyze it for human faces or people.

IMPORTANT: First, determine if there is a clearly visible human person or face in this image. Look for:
- Human faces (any age, any angle)
- Human figures or bodies
- People in the background or foreground
- Partial views of people

If you can see ANY human person or face, set hasPersonDetected to true and estimate their age.
If you cannot see any human person or face clearly, set hasPersonDetected to false and estimatedAge to null.

Please respond with a JSON object containing:
- estimatedAge: number (the estimated age in years, or null if no person detected)
- confidence: number (confidence level from 0 to 1)
- hasPersonDetected: boolean (true if ANY person is clearly visible)
- explanation: string (explanation of your age analysis)`;return"basic"===e?t+`

Provide a brief, simple explanation of your age estimation.

Example response:
{
  "estimatedAge": 25,
  "confidence": 0.7,
  "hasPersonDetected": true,
  "explanation": "I can see a person who appears to be in their mid-twenties based on their facial features and overall appearance."
}`:"detailed"===e?t+`

For the age estimation, provide a SCIENTIFIC but EASY-TO-UNDERSTAND explanation that covers:

1. **Facial Structure Analysis**: Describe bone structure, facial proportions, jawline definition
2. **Skin Analysis**: Comment on skin texture, elasticity, presence of wrinkles, fine lines, or age spots
3. **Eye Area**: Analyze crow's feet, under-eye area, eyelid changes
4. **Hair Analysis**: Hair texture, color, hairline, presence of gray hair
5. **Overall Facial Maturity**: General facial development and maturity indicators

Make the explanation sound professional but accessible, like a friendly expert explaining their analysis.

Example response:
{
  "estimatedAge": 28,
  "confidence": 0.85,
  "hasPersonDetected": true,
  "explanation": "Based on my analysis of facial characteristics, I estimate this person to be around 28 years old. Here's my scientific assessment: **Facial Structure**: The bone structure shows full adult development with well-defined cheekbones and a mature jawline, indicating someone past their early twenties. **Skin Analysis**: The skin appears smooth with good elasticity and minimal signs of aging - no significant wrinkles or age spots are visible, suggesting someone in their late twenties rather than thirties. **Eye Area**: The eyes show slight maturity with minimal crow's feet, consistent with late twenties. **Hair**: The hair appears full and healthy without visible gray, supporting the younger adult age range. **Overall Assessment**: The combination of mature facial structure with youthful skin characteristics points to someone in their late twenties, specifically around 28 years old."
}`:t+`

For the age estimation, provide a COMPREHENSIVE PROFESSIONAL ANALYSIS that covers:

1. **Detailed Facial Structure Analysis**:
   - Bone structure maturity and development
   - Facial proportions and symmetry
   - Jawline definition and muscle tone
   - Cheekbone prominence and facial volume

2. **Advanced Skin Analysis**:
   - Skin texture, elasticity, and firmness
   - Presence of wrinkles, fine lines, age spots, or sun damage
   - Skin tone evenness and pigmentation
   - Collagen and elastin indicators

3. **Comprehensive Eye Area Assessment**:
   - Crow's feet and laugh lines
   - Under-eye area (bags, dark circles, puffiness)
   - Eyelid changes and drooping
   - Eye brightness and clarity

4. **Detailed Hair and Scalp Analysis**:
   - Hair texture, thickness, and health
   - Hair color and presence of gray hair
   - Hairline position and recession patterns
   - Overall hair volume and density

5. **Lifestyle and Environmental Indicators**:
   - Signs of sun exposure or environmental damage
   - Indicators of stress, health, or lifestyle factors
   - Muscle tone and facial expression patterns

6. **Comparative Age Assessment**:
   - How features compare to typical age ranges
   - Confidence intervals and uncertainty factors
   - Alternative age possibilities and reasoning

Make this sound like a professional dermatologist or forensic age analyst explaining their detailed findings.

Example response:
{
  "estimatedAge": 32,
  "confidence": 0.88,
  "hasPersonDetected": true,
  "explanation": "Based on comprehensive facial analysis, I estimate this individual to be approximately 32 years old (\xb13 years). **Detailed Facial Structure**: The bone structure demonstrates full skeletal maturity with well-developed zygomatic arches and a fully defined mandible, indicating completion of facial development typically seen after age 25. The facial proportions show mature adult characteristics with good symmetry. **Advanced Skin Analysis**: The skin exhibits good elasticity with minimal photoaging. I observe very fine periorbital lines and slight nasolabial fold development, consistent with early thirties. No significant age spots or deep wrinkles are present, suggesting good skin care and limited sun damage. **Eye Area Assessment**: Minimal crow's feet are visible during expression, and the periorbital area shows slight volume loss typical of the early thirties. No significant under-eye changes or lid ptosis observed. **Hair Analysis**: Hair appears full and healthy with natural color variation but no visible gray, supporting the younger adult assessment. Hairline shows no recession patterns. **Lifestyle Indicators**: Overall facial muscle tone appears good, suggesting active lifestyle. No significant environmental damage indicators. **Comparative Assessment**: The combination of mature bone structure with relatively youthful soft tissue characteristics strongly supports an age estimate in the early thirties range, specifically around 32 years old."
}`}(s.promptComplexity),r=await nu.chat.completions.create({model:s.model,messages:[{role:"user",content:[{type:"text",text:n},{type:"image_url",image_url:{url:`data:image/jpeg;base64,${e}`,detail:s.imageDetail}}]}],max_tokens:s.maxTokens,temperature:.1}),i=r.choices[0]?.message?.content?.trim();if(!i)throw Error("No response from OpenAI");console.log("OpenAI raw response:",i);try{let e=i,t=i.match(/\{[\s\S]*\}/);t&&(e=t[0]);let s=JSON.parse(e);if("boolean"!=typeof s.hasPersonDetected)throw console.log("Invalid response structure:",s),Error("Invalid response format - hasPersonDetected is not boolean");return{estimatedAge:null===s.estimatedAge?null:"number"==typeof s.estimatedAge&&s.estimatedAge>0&&s.estimatedAge<150?Math.round(s.estimatedAge):null,confidence:Math.max(0,Math.min(1,Number(s.confidence)||0)),hasPersonDetected:!!s.hasPersonDetected,explanation:String(s.explanation||"No explanation provided")}}catch(a){console.log("JSON parsing failed:",a),console.log("Attempting text analysis fallback...");let e=i.toLowerCase(),t=e.includes("person")||e.includes("face")||e.includes("human")||e.includes("people")||e.includes("individual")||e.includes("man")||e.includes("woman")||e.includes("child")||e.includes("adult"),s=i.match(/(\d+)\s*years?\s*old/i)||i.match(/age.*?(\d+)/i)||i.match(/(\d+).*?age/i)||i.match(/around\s*(\d+)/i)||i.match(/approximately\s*(\d+)/i),n=s?parseInt(s[1]):null,r=i.substring(0,300)+(i.length>300?"...":"");return r=t&&n?`Based on my analysis, I estimate this person to be around ${n} years old. ${r}`:t?`I can detect a person in the image, but I'm having difficulty determining their exact age. ${r}`:`I cannot clearly detect a person in this image. ${r}`,{estimatedAge:t&&n&&n>0&&n<150?n:null,confidence:.6*!!t,hasPersonDetected:t,explanation:r}}}catch(e){return console.error("OpenAI API error:",e),{estimatedAge:null,confidence:0,hasPersonDetected:!1,explanation:"Error analyzing image",error:e instanceof Error?e.message:"Unknown error"}}}var np=s(2049),nf=s(974);async function nm(e){try{let t=await (0,eT.d)(),{data:{user:s}}=await t.auth.getUser(),n=s?.id,r=(0,nf.Tf)(e),i=await (0,np.hS)(n,r);if(!i.canUse)return eN.NextResponse.json({error:i.needsCredits?"You have reached your daily limit. Purchase credits to continue.":"Daily limit reached. Please try again tomorrow or create an account for more uses.",needsCredits:i.needsCredits,isAnonymous:i.isAnonymous},{status:429});let a=await e.formData(),o=a.get("image"),l=a.get("detailLevel");if(!o)return eN.NextResponse.json({error:"No image file provided"},{status:400});let c=l&&["low","medium","high"].includes(l)?l:"medium",u=nh[c];if(!i.canUseLevel?.[c])return eN.NextResponse.json({error:`Insufficient credits for ${u.name}. This analysis requires ${u.credits} credits.`,needsCredits:!0,isAnonymous:i.isAnonymous,requiredCredits:u.credits},{status:429});let h=["image/jpeg","image/jpg","image/png","image/webp"].includes(o.type)?o.size>0xa00000?{valid:!1,error:"Image file is too large. Please upload an image smaller than 10MB"}:{valid:!0}:{valid:!1,error:"Please upload a valid image file (JPEG, PNG, or WebP)"};if(!h.valid)return eN.NextResponse.json({error:h.error},{status:400});let d=await o.arrayBuffer(),p=Buffer.from(d).toString("base64"),f=await nd(p,c);if(f.error)return eN.NextResponse.json({error:f.error},{status:500});if(!await (0,np.SQ)(n,r,u.credits))return eN.NextResponse.json({error:"Failed to process request. Please try again."},{status:500});let m={user_id:n,image_url:null,estimated_age:f.estimatedAge,confidence_score:f.confidence,analysis_result:{hasPersonDetected:f.hasPersonDetected,explanation:f.explanation,timestamp:new Date().toISOString()}},{data:g,error:y}=await t.from("photo_analyses").insert(m).select().single();y&&console.error("Error saving analysis:",y);let w=await (0,np.hS)(n,r);return eN.NextResponse.json({success:!0,analysis:{estimatedAge:f.estimatedAge,confidence:f.confidence,hasPersonDetected:f.hasPersonDetected,explanation:f.explanation,detailLevel:c,creditsUsed:u.credits},usage:{remainingUses:w.remainingUses,isAnonymous:w.isAnonymous,needsCredits:w.needsCredits,canUseLevel:w.canUseLevel},analysisId:g?.id})}catch(e){return console.error("Analysis API error:",e),eN.NextResponse.json({error:"Internal server error"},{status:500})}}let ng=new eE.AppRouteRouteModule({definition:{kind:eP.RouteKind.APP_ROUTE,page:"/api/analyze/route",pathname:"/api/analyze",filename:"route",bundlePath:"app/api/analyze/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\analyze\\route.ts",nextConfigOutput:"",userland:eR}),{workAsyncStorage:ny,workUnitAsyncStorage:nw,serverHooks:n_}=ng;function nb(){return(0,eC.patchFetch)({workAsyncStorage:ny,workUnitAsyncStorage:nw})}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[447,580,410],()=>s(7437));module.exports=n})();