(()=>{var e={};e.id=347,e.ids=[347],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,s)=>{"use strict";function r(e){let t=e.headers.get("x-forwarded-for"),s=e.headers.get("x-real-ip"),r=e.headers.get("cf-connecting-ip");return r||s||(t?t.split(",")[0].trim():"127.0.0.1")}s.d(t,{Tf:()=>r})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2049:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>n,hS:()=>a,sy:()=>u});var r=s(3769),i=s(6621);async function a(e,t){let s=await (0,r.d)();if(e){let{data:t}=await s.from("user_profiles").select("*").eq("id",e).single();if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!1,needsCredits:!1};let r=new Date().toISOString().split("T")[0],i=t.last_use_date,a=t.daily_uses;i!==r&&(a=0,await s.from("user_profiles").update({daily_uses:0,last_use_date:r}).eq("id",e));let n=Math.max(0,3-a)+t.credits,u=n>0;return{canUse:u,remainingUses:n,isAnonymous:!1,needsCredits:!u,canUseLevel:{low:n>=1,medium:n>=2,high:n>=3}}}{if(!t)return{canUse:!1,remainingUses:0,isAnonymous:!0,needsCredits:!1};let e=new Date().toISOString().split("T")[0],s=(0,i.LE)(),{data:r}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e),a=1>(r?.length||0);return{canUse:a,remainingUses:+!!a,isAnonymous:!0,needsCredits:!1,canUseLevel:{low:a,medium:!1,high:!1}}}}async function n(e,t,s=1){let a=await (0,r.d)();if(e){let{data:t}=await a.from("user_profiles").select("*").eq("id",e).single();if(!t)return!1;let r=new Date().toISOString().split("T")[0],i=t.daily_uses;t.last_use_date!==r&&(i=0);let n=Math.max(0,3-i);if(n+t.credits<s)return!1;let u=s,o=i,d=t.credits;if(n>0){let e=Math.min(n,u);o+=e,u-=e}return u>0&&(d-=u),await a.from("user_profiles").update({daily_uses:o,credits:d,last_use_date:r,updated_at:new Date().toISOString()}).eq("id",e),!0}{if(!t)return!1;let e=new Date().toISOString().split("T")[0],s=(0,i.LE)(),{data:r}=await s.from("anonymous_uses").select("*").eq("ip_address",t).eq("use_date",e).single();return!r&&(await s.from("anonymous_uses").insert({ip_address:t,use_date:e}),!0)}}async function u(e,t){let s=await (0,r.d)(),{data:i}=await s.from("user_profiles").select("credits").eq("id",e).single();return!!i&&(await s.from("user_profiles").update({credits:i.credits+t,updated_at:new Date().toISOString()}).eq("id",e),!0)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(4386),i=s(4999);let a=async()=>{let e=await (0,i.UL)();return(0,r.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){e.set({name:t,value:s,...r})},remove(t,s){e.set({name:t,value:"",...s})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6549:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>p});var i=s(6559),a=s(8088),n=s(7719),u=s(2190),o=s(3769),d=s(2049),c=s(974);async function p(e){try{let t=await (0,o.d)(),{data:{user:s}}=await t.auth.getUser(),r=s?.id,i=(0,c.Tf)(e),a=await (0,d.hS)(r,i),n=null;if(r){let{data:e}=await t.from("user_profiles").select("*").eq("id",r).single();n=e}return u.NextResponse.json({success:!0,usage:{canUse:a.canUse,remainingUses:a.remainingUses,isAnonymous:a.isAnonymous,needsCredits:a.needsCredits,canUseLevel:a.canUseLevel},user:s?{id:s.id,email:s.email,profile:n}:null})}catch(e){return console.error("Usage API error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/usage/route",pathname:"/api/usage",filename:"route",bundlePath:"app/api/usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\usage\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=l;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},6621:(e,t,s)=>{"use strict";s.d(t,{LE:()=>u});var r=s(6437);s(4386);let i=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",a=()=>process.env.SUPABASE_SERVICE_ROLE_KEY||"",n=()=>{let e=i(),t=a();if(!e||!t)throw Error("Supabase URL and Service Role Key are required");return(0,r.UU)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})},u=()=>n()},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,410],()=>s(6549));module.exports=r})();