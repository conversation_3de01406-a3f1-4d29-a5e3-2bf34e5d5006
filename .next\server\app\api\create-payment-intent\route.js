(()=>{var e={};e.id=634,e.ids=[634],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3769:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});var s=r(4386),i=r(4999);let n=async()=>{let e=await (0,i.UL)();return(0,s.createServerClient)("https://gsuvqpwagpdwwcmtggyy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){e.set({name:t,value:r,...s})},remove(t,r){e.set({name:t,value:"",...r})}}})}},4075:e=>{"use strict";e.exports=require("zlib")},4264:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>p});var i=r(6559),n=r(8088),a=r(7719),o=r(2190),c=r(3769),u=r(6706);async function p(e){try{let t=await (0,c.d)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{packageType:i}=await e.json();if(!i||!u.C[i])return o.NextResponse.json({error:"Invalid package type"},{status:400});let{clientSecret:n,paymentIntentId:a}=await (0,u.f)(r.id,i,r.email||void 0),p=u.C[i];return await t.from("credit_transactions").insert({user_id:r.id,amount:p.price,credits_added:p.credits,stripe_payment_intent_id:a,status:"pending"}),o.NextResponse.json({clientSecret:n,paymentIntentId:a,package:p})}catch(e){return console.error("Payment intent creation error:",e),o.NextResponse.json({error:"Failed to create payment intent"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/create-payment-intent/route",pathname:"/api/create-payment-intent",filename:"route",bundlePath:"app/api/create-payment-intent/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\src\\app\\api\\create-payment-intent\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=d;function y(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6487:()=>{},6706:(e,t,r)=>{"use strict";r.d(t,{C:()=>i,f:()=>n,t6:()=>a});let s=new(r(7877)).A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-06-30.basil"}),i={basic:{name:"5 Credits",credits:5,price:500,description:"Perfect for occasional use"}};async function n(e,t,r){let n=i[t],a=await s.paymentIntents.create({amount:n.price,currency:"usd",metadata:{userId:e,packageType:t,credits:n.credits.toString()},receipt_email:r,description:`${n.name} - Age Analysis Credits`});return{clientSecret:a.client_secret,paymentIntentId:a.id}}function a(e,t){try{return s.webhooks.constructEvent(e,t,process.env.STRIPE_WEBHOOK_SECRET)}catch(e){return console.error("Webhook signature verification failed:",e),null}}},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9727:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,410,877],()=>r(4264));module.exports=s})();