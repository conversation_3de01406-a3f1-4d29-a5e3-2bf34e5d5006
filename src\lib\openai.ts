import OpenAI from 'openai'

// Debug: Check if API key is loaded
console.log('OPENAI_API_KEY exists:', !!process.env.OPENAI_API_KEY)
console.log('OPENAI_API_KEY length:', process.env.OPENAI_API_KEY?.length || 0)

// Ensure API key is available
const apiKey = process.env.OPENAI_API_KEY
if (!apiKey) {
  throw new Error('OPENAI_API_KEY environment variable is missing or empty')
}

const openai = new OpenAI({
  apiKey: apiKey,
})

function generatePromptForLevel(complexity: 'basic' | 'detailed' | 'comprehensive'): string {
  const basePrompt = `Look carefully at this image and analyze it for human faces or people.

IMPORTANT: First, determine if there is a clearly visible human person or face in this image. Look for:
- Human faces (any age, any angle)
- Human figures or bodies
- People in the background or foreground
- Partial views of people

If you can see ANY human person or face, set hasPersonDetected to true and estimate their age.
If you cannot see any human person or face clearly, set hasPersonDetected to false and estimatedAge to null.

Please respond with a JSON object containing:
- estimatedAge: number (the estimated age in years, or null if no person detected)
- confidence: number (confidence level from 0 to 1)
- hasPersonDetected: boolean (true if ANY person is clearly visible)
- explanation: string (explanation of your age analysis)`

  if (complexity === 'basic') {
    return basePrompt + `

Provide a brief, simple explanation of your age estimation.

Example response:
{
  "estimatedAge": 25,
  "confidence": 0.7,
  "hasPersonDetected": true,
  "explanation": "I can see a person who appears to be in their mid-twenties based on their facial features and overall appearance."
}`
  }

  if (complexity === 'detailed') {
    return basePrompt + `

For the age estimation, provide a SCIENTIFIC but EASY-TO-UNDERSTAND explanation that covers:

1. **Facial Structure Analysis**: Describe bone structure, facial proportions, jawline definition
2. **Skin Analysis**: Comment on skin texture, elasticity, presence of wrinkles, fine lines, or age spots
3. **Eye Area**: Analyze crow's feet, under-eye area, eyelid changes
4. **Hair Analysis**: Hair texture, color, hairline, presence of gray hair
5. **Overall Facial Maturity**: General facial development and maturity indicators

Make the explanation sound professional but accessible, like a friendly expert explaining their analysis.

Example response:
{
  "estimatedAge": 28,
  "confidence": 0.85,
  "hasPersonDetected": true,
  "explanation": "Based on my analysis of facial characteristics, I estimate this person to be around 28 years old. Here's my scientific assessment: **Facial Structure**: The bone structure shows full adult development with well-defined cheekbones and a mature jawline, indicating someone past their early twenties. **Skin Analysis**: The skin appears smooth with good elasticity and minimal signs of aging - no significant wrinkles or age spots are visible, suggesting someone in their late twenties rather than thirties. **Eye Area**: The eyes show slight maturity with minimal crow's feet, consistent with late twenties. **Hair**: The hair appears full and healthy without visible gray, supporting the younger adult age range. **Overall Assessment**: The combination of mature facial structure with youthful skin characteristics points to someone in their late twenties, specifically around 28 years old."
}`
  }

  // comprehensive
  return basePrompt + `

For the age estimation, provide a COMPREHENSIVE PROFESSIONAL ANALYSIS that covers:

1. **Detailed Facial Structure Analysis**:
   - Bone structure maturity and development
   - Facial proportions and symmetry
   - Jawline definition and muscle tone
   - Cheekbone prominence and facial volume

2. **Advanced Skin Analysis**:
   - Skin texture, elasticity, and firmness
   - Presence of wrinkles, fine lines, age spots, or sun damage
   - Skin tone evenness and pigmentation
   - Collagen and elastin indicators

3. **Comprehensive Eye Area Assessment**:
   - Crow's feet and laugh lines
   - Under-eye area (bags, dark circles, puffiness)
   - Eyelid changes and drooping
   - Eye brightness and clarity

4. **Detailed Hair and Scalp Analysis**:
   - Hair texture, thickness, and health
   - Hair color and presence of gray hair
   - Hairline position and recession patterns
   - Overall hair volume and density

5. **Lifestyle and Environmental Indicators**:
   - Signs of sun exposure or environmental damage
   - Indicators of stress, health, or lifestyle factors
   - Muscle tone and facial expression patterns

6. **Comparative Age Assessment**:
   - How features compare to typical age ranges
   - Confidence intervals and uncertainty factors
   - Alternative age possibilities and reasoning

Make this sound like a professional dermatologist or forensic age analyst explaining their detailed findings.

Example response:
{
  "estimatedAge": 32,
  "confidence": 0.88,
  "hasPersonDetected": true,
  "explanation": "Based on comprehensive facial analysis, I estimate this individual to be approximately 32 years old (±3 years). **Detailed Facial Structure**: The bone structure demonstrates full skeletal maturity with well-developed zygomatic arches and a fully defined mandible, indicating completion of facial development typically seen after age 25. The facial proportions show mature adult characteristics with good symmetry. **Advanced Skin Analysis**: The skin exhibits good elasticity with minimal photoaging. I observe very fine periorbital lines and slight nasolabial fold development, consistent with early thirties. No significant age spots or deep wrinkles are present, suggesting good skin care and limited sun damage. **Eye Area Assessment**: Minimal crow's feet are visible during expression, and the periorbital area shows slight volume loss typical of the early thirties. No significant under-eye changes or lid ptosis observed. **Hair Analysis**: Hair appears full and healthy with natural color variation but no visible gray, supporting the younger adult assessment. Hairline shows no recession patterns. **Lifestyle Indicators**: Overall facial muscle tone appears good, suggesting active lifestyle. No significant environmental damage indicators. **Comparative Assessment**: The combination of mature bone structure with relatively youthful soft tissue characteristics strongly supports an age estimate in the early thirties range, specifically around 32 years old."
}`
}

export interface AgeAnalysisResult {
  estimatedAge: number | null
  confidence: number
  hasPersonDetected: boolean
  explanation: string
  error?: string
}

export type DetailLevel = 'low' | 'medium' | 'high'

export interface DetailLevelConfig {
  name: string
  description: string
  credits: number
  model: string
  imageDetail: 'low' | 'high'
  maxTokens: number
  promptComplexity: 'basic' | 'detailed' | 'comprehensive'
}

export const DETAIL_LEVELS: Record<DetailLevel, DetailLevelConfig> = {
  low: {
    name: 'Basic Analysis',
    description: 'Quick age estimation with basic explanation',
    credits: 1,
    model: 'gpt-4o-mini',
    imageDetail: 'low',
    maxTokens: 300,
    promptComplexity: 'basic'
  },
  medium: {
    name: 'Detailed Analysis',
    description: 'Comprehensive age analysis with scientific explanation',
    credits: 2,
    model: 'gpt-4o',
    imageDetail: 'high',
    maxTokens: 500,
    promptComplexity: 'detailed'
  },
  high: {
    name: 'Expert Analysis',
    description: 'In-depth professional analysis with multiple factors',
    credits: 3,
    model: 'gpt-4o',
    imageDetail: 'high',
    maxTokens: 800,
    promptComplexity: 'comprehensive'
  }
}

export async function analyzeImageAge(imageBase64: string, detailLevel: DetailLevel = 'medium'): Promise<AgeAnalysisResult> {
  try {
    const config = DETAIL_LEVELS[detailLevel]
    const prompt = generatePromptForLevel(config.promptComplexity)

    const response = await openai.chat.completions.create({
      model: config.model,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: config.imageDetail
              }
            }
          ]
        }
      ],
      max_tokens: config.maxTokens,
      temperature: 0.1, // Low temperature for more consistent results
    })

    const content = response.choices[0]?.message?.content?.trim()
    if (!content) {
      throw new Error('No response from OpenAI')
    }

    console.log('OpenAI raw response:', content) // Debug log

    try {
      // Try to parse JSON response
      let jsonContent = content

      // Sometimes the response might have extra text before/after JSON
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        jsonContent = jsonMatch[0]
      }

      const result = JSON.parse(jsonContent)

      // Validate the response structure
      if (typeof result.hasPersonDetected !== 'boolean') {
        console.log('Invalid response structure:', result)
        throw new Error('Invalid response format - hasPersonDetected is not boolean')
      }

      // Ensure estimatedAge is valid
      const estimatedAge = result.estimatedAge === null ? null :
                          (typeof result.estimatedAge === 'number' && result.estimatedAge > 0 && result.estimatedAge < 150) ?
                          Math.round(result.estimatedAge) : null

      return {
        estimatedAge,
        confidence: Math.max(0, Math.min(1, Number(result.confidence) || 0)),
        hasPersonDetected: Boolean(result.hasPersonDetected),
        explanation: String(result.explanation || 'No explanation provided')
      }
    } catch (parseError) {
      console.log('JSON parsing failed:', parseError)
      console.log('Attempting text analysis fallback...')

      // If JSON parsing fails, try to extract information from text
      const lowerContent = content.toLowerCase()

      // More comprehensive person detection
      const hasPersonMatch = lowerContent.includes('person') ||
                           lowerContent.includes('face') ||
                           lowerContent.includes('human') ||
                           lowerContent.includes('people') ||
                           lowerContent.includes('individual') ||
                           lowerContent.includes('man') ||
                           lowerContent.includes('woman') ||
                           lowerContent.includes('child') ||
                           lowerContent.includes('adult')

      // More comprehensive age extraction
      const ageMatch = content.match(/(\d+)\s*years?\s*old/i) ||
                      content.match(/age.*?(\d+)/i) ||
                      content.match(/(\d+).*?age/i) ||
                      content.match(/around\s*(\d+)/i) ||
                      content.match(/approximately\s*(\d+)/i)

      const estimatedAge = ageMatch ? parseInt(ageMatch[1]) : null

      // Create a more structured explanation even for fallback
      let fallbackExplanation = content.substring(0, 300) + (content.length > 300 ? '...' : '')

      if (hasPersonMatch && estimatedAge) {
        fallbackExplanation = `Based on my analysis, I estimate this person to be around ${estimatedAge} years old. ${fallbackExplanation}`
      } else if (hasPersonMatch) {
        fallbackExplanation = `I can detect a person in the image, but I'm having difficulty determining their exact age. ${fallbackExplanation}`
      } else {
        fallbackExplanation = `I cannot clearly detect a person in this image. ${fallbackExplanation}`
      }

      return {
        estimatedAge: hasPersonMatch && estimatedAge && estimatedAge > 0 && estimatedAge < 150 ? estimatedAge : null,
        confidence: hasPersonMatch ? 0.6 : 0,
        hasPersonDetected: hasPersonMatch,
        explanation: fallbackExplanation
      }
    }
  } catch (error) {
    console.error('OpenAI API error:', error)
    
    return {
      estimatedAge: null,
      confidence: 0,
      hasPersonDetected: false,
      explanation: 'Error analyzing image',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Helper function to convert File to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate image file
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024 // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  
  if (!allowedTypes.includes(file.type)) {
    return { 
      valid: false, 
      error: 'Please upload a valid image file (JPEG, PNG, or WebP)' 
    }
  }
  
  if (file.size > maxSize) {
    return { 
      valid: false, 
      error: 'Image file is too large. Please upload an image smaller than 10MB' 
    }
  }
  
  return { valid: true }
}
