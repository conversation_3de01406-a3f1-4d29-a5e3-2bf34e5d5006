'use client'

import { useState } from 'react'
import { Camera, History, LogIn } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import PhotoUpload from '@/components/PhotoUpload'
import AnalysisHistory from '@/components/AnalysisHistory'
import UserProfile from '@/components/UserProfile'
import AuthModal from '@/components/AuthModal'
import BuyCreditsModal from '@/components/BuyCreditsModal'

export default function Home() {
  const [activeTab, setActiveTab] = useState<'upload' | 'history'>('upload')
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [showCreditsModal, setShowCreditsModal] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')
  const { user, loading } = useAuth()

  const handleNeedAuth = () => {
    setAuthMode('signup')
    setShowAuthModal(true)
  }

  const handleNeedCredits = () => {
    if (!user) {
      handleNeedAuth()
    } else {
      setShowCreditsModal(true)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
                <Camera className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Guess My Age</h1>
                <p className="text-sm text-gray-500">AI-powered age estimation</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {user ? (
                <UserProfile onBuyCredits={() => setShowCreditsModal(true)} />
              ) : (
                <button
                  onClick={() => {
                    setAuthMode('signin')
                    setShowAuthModal(true)
                  }}
                  className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <LogIn className="w-4 h-4" />
                  <span>Sign In</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-white rounded-lg p-1 mb-8 shadow-sm max-w-md mx-auto">
          <button
            onClick={() => setActiveTab('upload')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
              activeTab === 'upload'
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Camera className="w-4 h-4" />
            <span>Upload Photo</span>
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
              activeTab === 'history'
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <History className="w-4 h-4" />
            <span>History</span>
          </button>
        </div>

        {/* Tab Content */}
        <div className="max-w-4xl mx-auto">
          {activeTab === 'upload' ? (
            <PhotoUpload
              onNeedAuth={handleNeedAuth}
              onNeedCredits={handleNeedCredits}
            />
          ) : (
            <AnalysisHistory />
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-gray-600 text-sm">
              Powered by OpenAI GPT-4o-mini • Built with Next.js and Supabase
            </p>
            <p className="text-gray-500 text-xs mt-2">
              Your photos are analyzed securely and not stored on our servers
            </p>
          </div>
        </div>
      </footer>

      {/* Modals */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode={authMode}
      />

      <BuyCreditsModal
        isOpen={showCreditsModal}
        onClose={() => setShowCreditsModal(false)}
      />
    </div>
  )
}
