(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1814:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>o,A:()=>x});var a=t(5155),l=t(2115),r=t(3865);t(9509);let i=()=>"https://gsuvqpwagpdwwcmtggyy.supabase.co",n=()=>"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY",c=()=>{let e=i(),s=n();if(!e||!s)throw Error("Supabase URL and Anon Key are required");return(0,r.createBrowserClient)(e,s)},d=(0,l.createContext)(void 0);function o(e){let{children:s}=e,[t,r]=(0,l.useState)(null),[i,n]=(0,l.useState)(null),[o,x]=(0,l.useState)(!0),[m]=(0,l.useState)(()=>c()),u=(0,l.useCallback)(async e=>{try{let{data:s,error:t}=await m.from("user_profiles").select("*").eq("id",e).single();if(t)return void console.error("Error fetching profile:",t);n(s)}catch(e){console.error("Error fetching profile:",e)}},[m]),h=async()=>{t&&await u(t.id)};(0,l.useEffect)(()=>{(async()=>{var e;let{data:{session:s}}=await m.auth.getSession();r(null!=(e=null==s?void 0:s.user)?e:null),(null==s?void 0:s.user)&&await u(s.user.id),x(!1)})();let{data:{subscription:e}}=m.auth.onAuthStateChange(async(e,s)=>{var t;r(null!=(t=null==s?void 0:s.user)?t:null),(null==s?void 0:s.user)?await u(s.user.id):n(null),x(!1)});return()=>e.unsubscribe()},[u,m.auth]);let g=async(e,s)=>{let{error:t}=await m.auth.signInWithPassword({email:e,password:s});return{error:t}},b=async(e,s)=>{let{error:t}=await m.auth.signUp({email:e,password:s});return{error:t}},p=async()=>{await m.auth.signOut()};return(0,a.jsx)(d.Provider,{value:{user:t,profile:i,loading:o,signIn:g,signUp:b,signOut:p,refreshProfile:h},children:s})}function x(){let e=(0,l.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2164:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a,l=t(5155),r=t(2115),i=t(4355),n=t(306),c=t(9676),d=t(1814),o=t(381),x=t(646),m=t(5339),u=t(9869),h=t(1007),g=t(4186),b=t(9617),p=t(9509);console.log("OPENAI_API_KEY exists:",!!p.env.OPENAI_API_KEY),console.log("OPENAI_API_KEY length:",(null==(a=p.env.OPENAI_API_KEY)?void 0:a.length)||0);let j=p.env.OPENAI_API_KEY;if(!j)throw Error("OPENAI_API_KEY environment variable is missing or empty");new b.Ay({apiKey:j});let y={low:{name:"Basic Analysis",description:"Quick age estimation with basic explanation",credits:1,model:"gpt-4o-mini",imageDetail:"low",maxTokens:300,promptComplexity:"basic"},medium:{name:"Detailed Analysis",description:"Comprehensive age analysis with scientific explanation",credits:2,model:"gpt-4o",imageDetail:"high",maxTokens:500,promptComplexity:"detailed"},high:{name:"Expert Analysis",description:"In-depth professional analysis with multiple factors",credits:3,model:"gpt-4o",imageDetail:"high",maxTokens:800,promptComplexity:"comprehensive"}};function f(e){let{onNeedAuth:s,onNeedCredits:t}=e,[a,n]=(0,r.useState)(null),[c,b]=(0,r.useState)(null),[p,j]=(0,r.useState)(!1),[f,N]=(0,r.useState)(null),[v,w]=(0,r.useState)(null),[A,C]=(0,r.useState)(""),[S,k]=(0,r.useState)("medium"),E=(0,r.useRef)(null),{}=(0,d.A)(),P=async e=>{n(e),N(null),C("");let s=new FileReader;s.onload=e=>{var s;b(null==(s=e.target)?void 0:s.result)},s.readAsDataURL(e);try{let e=await fetch("/api/usage");if(e.ok){var t;let s=await e.json();w(s.usage),s.usage.isAnonymous?k("low"):(null==(t=s.usage.canUseLevel)?void 0:t.medium)||k("low")}}catch(e){console.error("Failed to fetch usage info:",e)}},I=async()=>{if(a){j(!0),C("");try{let e=new FormData;e.append("image",a),e.append("detailLevel",S);let l=await fetch("/api/analyze",{method:"POST",body:e}),r=await l.json();if(!l.ok)throw 429===l.status&&(r.needsCredits?t():r.isAnonymous&&s()),Error(r.error||"Analysis failed");N(r.analysis),w(r.usage)}catch(e){C(e instanceof Error?e.message:"An error occurred")}finally{j(!1)}}};return(0,l.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,l.jsx)("div",{onDrop:e=>{e.preventDefault();let s=e.dataTransfer.files;s.length>0&&P(s[0])},onDragOver:e=>e.preventDefault(),className:"border-2 border-dashed rounded-2xl p-8 text-center transition-colors ".concat(c?"border-blue-300 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"),children:c?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("img",{src:c,alt:"Preview",className:"max-w-full max-h-64 mx-auto rounded-lg shadow-md"}),(0,l.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)(o.A,{className:"w-4 h-4 text-gray-600"}),(0,l.jsx)("h4",{className:"font-medium text-gray-900",children:"Analysis Detail Level"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 gap-2",children:Object.entries(y).map(e=>{var s;let[t,a]=e,r=(null==v||null==(s=v.canUseLevel)?void 0:s[t])!==!1,i=S===t;return(0,l.jsx)("button",{onClick:()=>k(t),disabled:!r||p,className:"p-3 rounded-lg border-2 text-left transition-all ".concat(i?"border-blue-500 bg-blue-50":r?"border-gray-200 bg-white hover:border-gray-300":"border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed"),children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("span",{className:"font-medium ".concat(i?"text-blue-900":r?"text-gray-900":"text-gray-500"),children:a.name}),(0,l.jsxs)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(i?"bg-blue-100 text-blue-700":r?"bg-gray-100 text-gray-600":"bg-gray-50 text-gray-400"),children:[a.credits," credit",a.credits>1?"s":""]})]}),(0,l.jsx)("p",{className:"text-sm mt-1 ".concat(i?"text-blue-700":r?"text-gray-600":"text-gray-400"),children:a.description})]}),i&&(0,l.jsx)(x.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0"})]})},t)})}),(null==v?void 0:v.isAnonymous)&&(0,l.jsxs)("p",{className:"text-xs text-amber-600 bg-amber-50 p-2 rounded",children:[(0,l.jsx)(m.A,{className:"w-3 h-3 inline mr-1"}),"Anonymous users can only use Basic Analysis. Sign up for more options!"]})]}),(0,l.jsxs)("div",{className:"flex justify-center space-x-3",children:[(0,l.jsx)("button",{onClick:I,disabled:p,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:p?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),(0,l.jsx)("span",{children:"Analyzing..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Analyze Age"})]})}),(0,l.jsx)("button",{onClick:()=>{n(null),b(null),N(null),C(""),E.current&&(E.current.value="")},className:"bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"Choose Different Photo"})]})]}):(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto",children:(0,l.jsx)(u.A,{className:"w-8 h-8 text-blue-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Upload a Photo"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Drag and drop your photo here, or click to select"}),(0,l.jsx)("button",{onClick:()=>{var e;return null==(e=E.current)?void 0:e.click()},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Select Photo"})]}),(0,l.jsx)("input",{ref:E,type:"file",accept:"image/*",onChange:e=>{let s=e.target.files;s&&s.length>0&&P(s[0])},className:"hidden"})]})}),A&&(0,l.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3",children:[(0,l.jsx)(m.A,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-red-900",children:"Error"}),(0,l.jsx)("p",{className:"text-red-700 text-sm",children:A})]})]}),f&&(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-2xl p-6 shadow-sm",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(x.A,{className:"w-5 h-5 text-green-600"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Complete"})]}),f.detailLevel&&f.creditsUsed&&(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("div",{className:"text-sm text-gray-600",children:y[f.detailLevel].name}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[f.creditsUsed," credit",f.creditsUsed>1?"s":""," used"]})]})]}),f.hasPersonDetected?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)(h.A,{className:"w-4 h-4 text-blue-600"}),(0,l.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Estimated Age"})]}),(0,l.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[f.estimatedAge," years"]})]}),(0,l.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)(g.A,{className:"w-4 h-4 text-green-600"}),(0,l.jsx)("span",{className:"text-sm font-medium text-green-900",children:"Confidence"})]}),(0,l.jsxs)("div",{className:"text-2xl font-bold text-green-900",children:[Math.round(100*f.confidence),"%"]})]})]}),(0,l.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,l.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,l.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Scientific Analysis"]}),(0,l.jsx)("div",{className:"text-gray-700 text-sm leading-relaxed space-y-2",children:f.explanation.split("**").map((e,s)=>s%2==1?(0,l.jsx)("span",{className:"font-semibold text-gray-900",children:e},s):e.split("\n").map((t,a)=>(0,l.jsxs)("span",{children:[t,a<e.split("\n").length-1&&(0,l.jsx)("br",{})]},"".concat(s,"-").concat(a))))})]})]}):(0,l.jsxs)("div",{className:"text-center py-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,l.jsx)(m.A,{className:"w-6 h-6 text-yellow-600"})}),(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"No Person Detected"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:f.explanation})]})]}),v&&(0,l.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-blue-900",children:v.isAnonymous?"Daily Limit":"Remaining Uses"}),(0,l.jsx)("p",{className:"text-sm text-blue-700",children:v.isAnonymous?"".concat(v.remainingUses," free analysis remaining today"):"".concat(v.remainingUses," analyses remaining")})]}),v.needsCredits&&(0,l.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm",children:"Buy Credits"})]})})]})}var N=t(2355),v=t(3052);function w(){let[e,s]=(0,r.useState)([]),[t,a]=(0,r.useState)(!0),[i,n]=(0,r.useState)(""),[o,x]=(0,r.useState)(1),[u,b]=(0,r.useState)(1),{user:p}=(0,d.A)(),j=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;if(p){a(!0),n("");try{let t=await fetch("/api/history?page=".concat(e,"&limit=10"));if(!t.ok)throw Error("Failed to fetch history");let a=await t.json();s(a.analyses),x(a.pagination.page),b(a.pagination.totalPages)}catch(e){n(e instanceof Error?e.message:"An error occurred")}finally{a(!1)}}},[p]);(0,r.useEffect)(()=>{j()},[p,j]);let y=e=>{j(e)};return p?t?(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Loading your history..."})]}):i?(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(m.A,{className:"w-8 h-8 text-red-500"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error Loading History"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:i}),(0,l.jsx)("button",{onClick:()=>j(o),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Try Again"})]}):0===e.length?(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(c.A,{className:"w-8 h-8 text-gray-400"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Analysis History"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Your age analysis history will appear here after you upload photos."})]}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(c.A,{className:"w-4 h-4 text-blue-600"})}),(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Analysis History"})]}),(0,l.jsx)("div",{className:"space-y-4",children:e.map(e=>{var s,t;return(0,l.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:(0,l.jsx)("div",{className:"flex items-start justify-between",children:(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"flex items-center space-x-3 mb-2",children:e.estimated_age?(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(h.A,{className:"w-4 h-4 text-blue-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("span",{className:"font-semibold text-gray-900",children:[e.estimated_age," years old"]}),(0,l.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",Math.round(100*(e.confidence_score||0)),"% confidence)"]})]})]}):(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(m.A,{className:"w-4 h-4 text-yellow-600"})}),(0,l.jsx)("span",{className:"font-medium text-gray-900",children:"No person detected"})]})}),(null==(s=e.analysis_result)?void 0:s.explanation)&&(0,l.jsx)("div",{className:"text-sm text-gray-600 mb-2 leading-relaxed",children:e.analysis_result.explanation.split("**").map((e,s)=>s%2==1?(0,l.jsx)("span",{className:"font-semibold text-gray-800",children:e},s):e.split("\n").map((t,a)=>(0,l.jsxs)("span",{children:[t,a<e.split("\n").length-1&&(0,l.jsx)("br",{})]},"".concat(s,"-").concat(a))))}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[(0,l.jsx)(g.A,{className:"w-3 h-3"}),(0,l.jsx)("span",{children:(t=e.created_at,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(t)))})]})]})})},e.id)})}),u>1&&(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("button",{onClick:()=>y(o-1),disabled:o<=1,className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,l.jsx)(N.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Previous"})]}),(0,l.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",o," of ",u]}),(0,l.jsxs)("button",{onClick:()=>y(o+1),disabled:o>=u,className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,l.jsx)("span",{children:"Next"}),(0,l.jsx)(v.A,{className:"w-4 h-4"})]})]})]}):(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(c.A,{className:"w-8 h-8 text-gray-400"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sign In to View History"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Create an account to keep track of your age analyses."})]})}var A=t(9074),C=t(1586),S=t(4835);function k(e){var s,t;let{onBuyCredits:a}=e,{user:i,profile:n,signOut:c}=(0,d.A)(),[o,x]=(0,r.useState)(!1);if(!i||!n)return null;let m=Math.max(0,3-n.daily_uses),u=n.last_use_date!==new Date().toISOString().split("T")[0];return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("button",{onClick:()=>x(!o),className:"flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 hover:bg-gray-50 transition-colors",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(h.A,{className:"w-4 h-4 text-blue-600"})}),(0,l.jsxs)("div",{className:"text-left",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900",children:null==(s=i.email)?void 0:s.split("@")[0]}),(0,l.jsx)("div",{className:"text-xs text-gray-500",children:1===(t=n.credits)?"1 credit":"".concat(t," credits")})]})]}),o&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>x(!1)}),(0,l.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20",children:[(0,l.jsx)("div",{className:"p-4 border-b border-gray-100",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(h.A,{className:"w-5 h-5 text-blue-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium text-gray-900",children:i.email}),(0,l.jsxs)("div",{className:"text-sm text-gray-500",children:["Member since ",new Date(n.created_at).toLocaleDateString()]})]})]})}),(0,l.jsxs)("div",{className:"p-4 space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(A.A,{className:"w-4 h-4 text-blue-600"}),(0,l.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Daily Uses"})]}),(0,l.jsxs)("div",{className:"text-lg font-bold text-blue-900 mt-1",children:[u?3:m,"/3"]}),(0,l.jsx)("div",{className:"text-xs text-blue-700",children:u?"Refreshed today":"Remaining today"})]}),(0,l.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(C.A,{className:"w-4 h-4 text-green-600"}),(0,l.jsx)("span",{className:"text-sm font-medium text-green-900",children:"Credits"})]}),(0,l.jsx)("div",{className:"text-lg font-bold text-green-900 mt-1",children:n.credits}),(0,l.jsx)("div",{className:"text-xs text-green-700",children:"Extra analyses"})]})]}),(0,l.jsxs)("button",{onClick:()=>{a(),x(!1)},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2",children:[(0,l.jsx)(C.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Buy More Credits"})]}),(0,l.jsxs)("button",{onClick:()=>{c(),x(!1)},className:"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2",children:[(0,l.jsx)(S.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Sign Out"})]})]})]})]})]})}var E=t(313),P=t(4416),I=t(8883),_=t(2919);function D(e){let{isOpen:s,onClose:t,initialMode:a="signin"}=e,[i,n]=(0,r.useState)(a),[c,o]=(0,r.useState)(""),[x,m]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[g,b]=(0,r.useState)(""),{signIn:p,signUp:j}=(0,d.A)(),y=async e=>{e.preventDefault(),h(!0),b("");try{let{error:e}="signin"===i?await p(c,x):await j(c,x);e?b(e.message):"signup"===i?b("Check your email for the confirmation link!"):t()}catch(e){b("An unexpected error occurred")}finally{h(!1)}},f=()=>{o(""),m(""),b("")};return(0,l.jsxs)(E.lG,{open:s,onClose:t,className:"relative z-50",children:[(0,l.jsx)("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),(0,l.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,l.jsxs)(E.lG.Panel,{className:"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsx)(E.lG.Title,{className:"text-xl font-semibold",children:"signin"===i?"Sign In":"Create Account"}),(0,l.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(P.A,{className:"w-5 h-5"})})]}),(0,l.jsxs)("form",{onSubmit:y,className:"p-6 space-y-4",children:[g&&(0,l.jsx)("div",{className:"p-3 rounded-lg text-sm ".concat(g.includes("Check your email")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"),children:g}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(I.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,l.jsx)("input",{id:"email",type:"email",value:c,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,l.jsx)("input",{id:"password",type:"password",value:x,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0,minLength:6})]})]}),(0,l.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?"Loading...":"signin"===i?"Sign In":"Create Account"}),(0,l.jsx)("div",{className:"text-center",children:(0,l.jsx)("button",{type:"button",onClick:()=>{n("signin"===i?"signup":"signin"),f()},className:"text-sm text-blue-600 hover:text-blue-700 transition-colors",children:"signin"===i?"Don't have an account? Sign up":"Already have an account? Sign in"})})]})]})})]})}var O=t(5196),T=t(7368),U=t(5855);let z=(0,T.c)("your_stripe_publishable_key_here");function L(e){let{onSuccess:s,onError:t}=e,a=(0,U.useStripe)(),i=(0,U.useElements)(),[n,c]=(0,r.useState)(!1),{refreshProfile:o}=(0,d.A)(),x=async e=>{if(e.preventDefault(),a&&i){c(!0);try{let e=await fetch("/api/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({packageType:"basic"})}),{clientSecret:l,error:r}=await e.json();if(r)return void t(r);let{error:n}=await a.confirmCardPayment(l,{payment_method:{card:i.getElement(U.CardElement)}});n?t(n.message||"Payment failed"):(await o(),s())}catch(e){t("Payment processing failed")}finally{c(!1)}}};return(0,l.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,l.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-blue-900",children:"5 Credits Package"}),(0,l.jsx)("p",{className:"text-sm text-blue-700",children:"Perfect for occasional use"})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:"$5.00"}),(0,l.jsx)("div",{className:"text-sm text-blue-700",children:"$1.00 per credit"})]})]})}),(0,l.jsx)("div",{className:"border border-gray-300 rounded-lg p-3",children:(0,l.jsx)(U.CardElement,{options:{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}}}}})}),(0,l.jsxs)("button",{type:"submit",disabled:!a||n,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:[(0,l.jsx)(C.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:n?"Processing...":"Buy 5 Credits for $5.00"})]})]})}function F(e){let{isOpen:s,onClose:t}=e,[a,i]=(0,r.useState)(!1),[n,c]=(0,r.useState)(""),d=()=>{i(!1),c(""),t()};return(0,l.jsxs)(E.lG,{open:s,onClose:d,className:"relative z-50",children:[(0,l.jsx)("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),(0,l.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,l.jsxs)(E.lG.Panel,{className:"mx-auto max-w-md w-full bg-white rounded-2xl shadow-xl",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsx)(E.lG.Title,{className:"text-xl font-semibold",children:"Buy Credits"}),(0,l.jsx)("button",{onClick:d,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(P.A,{className:"w-5 h-5"})})]}),(0,l.jsx)("div",{className:"p-6",children:a?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(O.A,{className:"w-8 h-8 text-green-600"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Payment Successful!"}),(0,l.jsx)("p",{className:"text-gray-600",children:"5 credits have been added to your account."})]}):(0,l.jsxs)(l.Fragment,{children:[n&&(0,l.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-lg text-sm",children:n}),(0,l.jsx)(U.Elements,{stripe:z,children:(0,l.jsx)(L,{onSuccess:()=>{i(!0),c(""),setTimeout(()=>{i(!1),t()},2e3)},onError:e=>{c(e),i(!1)}})}),(0,l.jsx)("div",{className:"mt-4 text-xs text-gray-500 text-center",children:"Your payment is secured by Stripe. We don't store your card details."})]})})]})})]})}function M(){let[e,s]=(0,r.useState)("upload"),[t,a]=(0,r.useState)(!1),[o,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)("signin"),{user:h,loading:g}=(0,d.A)(),b=()=>{u("signup"),a(!0)};return g?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"})}):(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,l.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center",children:(0,l.jsx)(i.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Guess My Age"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"AI-powered age estimation"})]})]}),(0,l.jsx)("div",{className:"flex items-center space-x-4",children:h?(0,l.jsx)(k,{onBuyCredits:()=>x(!0)}):(0,l.jsxs)("button",{onClick:()=>{u("signin"),a(!0)},className:"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,l.jsx)(n.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Sign In"})]})})]})})}),(0,l.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"flex space-x-1 bg-white rounded-lg p-1 mb-8 shadow-sm max-w-md mx-auto",children:[(0,l.jsxs)("button",{onClick:()=>s("upload"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ".concat("upload"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:[(0,l.jsx)(i.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Upload Photo"})]}),(0,l.jsxs)("button",{onClick:()=>s("history"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ".concat("history"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:[(0,l.jsx)(c.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"History"})]})]}),(0,l.jsx)("div",{className:"max-w-4xl mx-auto",children:"upload"===e?(0,l.jsx)(f,{onNeedAuth:b,onNeedCredits:()=>{h?x(!0):b()}}):(0,l.jsx)(w,{})})]}),(0,l.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"Powered by OpenAI GPT-4o-mini • Built with Next.js and Supabase"}),(0,l.jsx)("p",{className:"text-gray-500 text-xs mt-2",children:"Your photos are analyzed securely and not stored on our servers"})]})})}),(0,l.jsx)(D,{isOpen:t,onClose:()=>a(!1),initialMode:m}),(0,l.jsx)(F,{isOpen:o,onClose:()=>x(!1)})]})}},7148:(e,s,t)=>{Promise.resolve().then(t.bind(t,2164))}},e=>{var s=s=>e(e.s=s);e.O(0,[865,81,441,684,358],()=>s(7148)),_N_E=e.O()}]);